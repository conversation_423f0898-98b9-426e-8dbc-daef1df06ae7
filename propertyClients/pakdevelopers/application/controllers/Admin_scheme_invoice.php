<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_scheme_invoice extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
	public function index($id)
	{
       
       $s=$this->db->get_where('schemes',array('id'=>$id));       
	   foreach($s->result() as $scheme){}
       
       $instalment=$this->db->query('SELECT
    `schemes`.`name` AS scheme_name
    , `plots`.`name` AS plote_name   
    ,`schemes`.*
    , `plots`.*
    , `instalment`.*
    ,CAST(instalment.`slip_number` AS UNSIGNED) AS order_slip
FROM
    `plots`
    INNER JOIN `instalment` 
        ON (`plots`.`id` = `instalment`.`plote_id`)
    INNER JOIN `schemes` 
        ON (`schemes`.`id` = `plots`.`scheme_id`)
        WHERE schemes.`id`=? ORDER BY order_slip ASC;',array($id))->result();
       
       $this->load->view('admin/view_scheme_invoice',array(
       'scheme'=>$scheme,
       'instalment'=>$instalment));
	}
    
 }