<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    } 
	public function index()
	{
	   $error="";
       if($this->input->post('submit'))
       {
          $this->form_validation->set_rules('name','Scheme Name','required|xss_clean');
          
          if($this->form_validation->run() !== false)
          {
            $this->db->insert('schemes',array(
            'name'=>$this->input->post('name'),
            'marlay'=>$this->input->post('marlay'),
            'feets'=>$this->input->post('feets'),
            'amount'=>$this->input->post('amount'),
            'first_amount'=>$this->input->post('first_amount')
            ));
            
            $error='<div class="alert alert-success">Scheme Inserted!</div>';
          }
       }
       
       
       $query=$this->db->get('schemes');
       
	   $this->load->view('admin/view_home',array('error'=>$error,'query'=>$query));
    }
    public function edit($id)
    {
        $error="";
       if($this->input->post('submit'))
       {
          $this->form_validation->set_rules('name','Scheme Name','required|xss_clean');
          if($this->form_validation->run() !== false)
          {
            $this->db->where('id',intval($id));
            $this->db->update('schemes',array(
            'name'=>$this->input->post('name'),
            'marlay'=>$this->input->post('marlay'),
            'feets'=>$this->input->post('feets'),
            'amount'=>$this->input->post('amount'),
            'first_amount'=>$this->input->post('first_amount')
            ));
            $error='<div class="alert alert-success">Scheme Updated!</div>';
            redirect(base_url('admin'));
          }
       }
        $g=$this->db->get_where('schemes',array('id'=>intval($id)));
        
        foreach($g->result() as $get){}
        
        $this->load->view('admin/view_edit_scheme',array('error'=>$error,'get'=>$get));
    }
    public function delete($id)
    {
        $this->db->where('id',intval($id));
        $this->db->delete('schemes');
        $this->db->where('scheme_id',intval($id));
        $p=$this->db->get('plots');
        
        $this->db->where('scheme_id',intval($id));
        $this->db->delete('development');
        
        foreach($p->result() as $row)
        {
            $this->db->where('id',$row->id);
            $this->db->delete('plots');
            
            $u=$this->db->get_where('users',array('plot_id'=>$row->id));
            foreach($u->result() as $row)
            {
                if(file_exists('uploads/'.$row->image))
                   unlink('uploads/'.$row->image);
            }
            
            $this->db->where('plot_id',$row->id);
            $this->db->delete('users');
            
            $this->db->where('plote_id',$row->id);
            $this->db->delete('instalment');
        }
        
        
        redirect(base_url('admin'));
    }
    private function set_upload_options()
    {   
       $config = array();
       $config['upload_path'] = 'uploads';
       $config['allowed_types'] = 'gif|jpg|png';
       $config['overwrite']     = FALSE;
      
       return $config;
    }
    public function shortPayment($user)
    {
        
        $user->rate_per_marla=(!empty($user->rate_per_marla_t)) ? $user->rate_per_marla_t :$user->rate_per_marla;
        $plote_id=$user->plot_id;
        $sdate=$user->sdate;
        $edate=$user->edate;
        $total_amount=((($user->rate_per_marla/225)*$user->square_feets)+($user->marlay*$user->rate_per_marla));
        $first_amount=(int)$user->first_amount+(int)$user->transfor_amount;
        $instalment_months=$user->instalment_months;
        
        
       $total_received=0;
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($plote_id),'user_cnic'=>$user->cnic));  
       foreach($i->result() as $row)
       {
          $total_received += $row->received_instalment;
       } 
        
       if($instalment_months==0) 
       {
          if($edate < date('Y-m-d'))
          {
            return ($total_amount-($total_received+$first_amount));
          }
          else
          {
            return 0;
          }
       }
        
        
        if($edate > date('Y-m-d'))
        {
            $sdate=explode('-',$sdate);
            $a = (((date('Y')-$sdate[0])-1)*12);
            $b = (date('m'))+(12-$sdate[1]);
        }
        else
        {
            $sdate=explode('-',$sdate);
            $edate=explode('-',$edate);
            $a = ((($edate[0]-$sdate[0])-1)*12);
            $b = ($edate[1])+(12-$sdate[1]);
        }
        
        $shortPayment = ($a+$b)
       *(($total_amount-$first_amount)/$instalment_months)
       +$first_amount-$total_amount
       +($total_amount-($total_received+$first_amount));
       
       return $shortPayment;
    }
    
 }