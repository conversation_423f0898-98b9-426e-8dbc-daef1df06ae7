﻿<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class search extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
    public function index()
	{
	   $query=$this->db->query("SELECT
    `schemes`.`name` AS scheme_name
    ,`schemes`.`id` AS sid
    , `users`.*
    , `plots`.`name` AS plote_name
FROM
    `schemes`
    INNER JOIN `plots` 
        ON (`schemes`.`id` = `plots`.`scheme_id`)
    INNER JOIN `users` 
        ON (`users`.`plot_id` = `plots`.`id`)
        WHERE users.`name` LIKE ?;",array('%'.$this->input->get('q').'%'));

        $this->load->view('admin/view_search',array('query'=>$query));
	}
    public function shortPayment($plote_id,$sdate,$edate,$total_amount,$first_amount,$instalment_months)
    {
        
       $total_received=0;
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($plote_id)));  
       foreach($i->result() as $row)
       {
          $total_received += $row->received_instalment;
       } 
        
       if($instalment_months==0) 
       {
          if($edate < date('Y-m-d'))
          {
            return ($total_amount-($total_received+$first_amount));
          }
          else
          {
            return 0;
          }
       }
        
        
        if($edate > date('Y-m-d'))
        {
            $sdate=explode('-',$sdate);
            $a = (((date('Y')-$sdate[0])-1)*12);
            $b = (date('m'))+(12-$sdate[1]);
        }
        else
        {
            $sdate=explode('-',$sdate);
            $edate=explode('-',$edate);
            $a = ((($edate[0]-$sdate[0])-1)*12);
            $b = ($edate[1])+(12-$sdate[1]);
        }
        
        $shortPayment = ($a+$b)
       *(($total_amount-$first_amount)/$instalment_months)
       +$first_amount-$total_amount
       +($total_amount-($total_received+$first_amount));
       
       return $shortPayment;
    }
}