<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_login extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if($this->session->userdata('is_admin'))
        {
            redirect(base_url('admin'));
        }
    }
	public function index()
	{
	   $error="";
	   if($this->input->post('login'))
       {
          $this->form_validation->set_rules('username', 'UserName', 'required|xss_clean');
  		  $this->form_validation->set_rules('password', 'Password', 'trim|required|md5|xss_clean');
          $this->form_validation->set_error_delimiters('<div class="alert alert-danger alert-dismissible fade in" role="alert">
    	            <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>', '</div>');
          
          if ($this->form_validation->run() == FALSE)
          {
              $this->load->view('admin/view_login',array('error'=>$error));
          }
          else
          {
             $data=$this->db->get_where('ra_admin',array(
             'ra_username'=> $this->input->post('username'),
             'ra_password'=> $this->input->post('password')
             ));
             
             if($data->num_rows()>0)
             {
                $this->session->set_userdata(array(
                'is_admin' =>true,
                'username' =>$this->input->post('username')
                ));
                
                redirect(base_url('admin'));
             }
             else
             {
                $error='<div class="alert alert-danger">Invalid username or password</div>';
                $this->load->view('admin/view_login',array('error'=>$error));
             }
          }
       }
       else
       {
          $this->load->view('admin/view_login',array('error'=>$error));
       }  
	}
 }