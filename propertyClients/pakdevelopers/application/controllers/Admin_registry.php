<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_registry extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
	public function index()
	{
	   $error="";
       
       if($this->input->post('Submit'))
       {
          $this->form_validation->set_rules('saler','saler','required|xss_clean');
          $this->form_validation->set_rules('pachaser','pachaser','required|xss_clean');
          $this->form_validation->set_rules('mobile','mobile','required|xss_clean');
          $this->form_validation->set_rules('scheme','scheme','required|xss_clean');
          $this->form_validation->set_rules('moza','moza','required|xss_clean');
          $this->form_validation->set_rules('dcrate','dcrate','required|xss_clean');
          $this->form_validation->set_rules('marlay','marlay','required|xss_clean');
          $this->form_validation->set_rules('total_price','total_price','required|xss_clean');
          $this->form_validation->set_rules('stam_submit_data','stam_submit_date','xss_clean');
          $this->form_validation->set_rules('stamm','stamm','xss_clean');
          $this->form_validation->set_rules('permition_submit_data','permition_submit_date','xss_clean');
          $this->form_validation->set_rules('permition','permition','xss_clean');
          $this->form_validation->set_rules('send','send','xss_clean');
          $this->form_validation->set_rules('registration_number','registration_number','xss_clean');
          $this->form_validation->set_rules('intakal','intakal','xss_clean');
          $this->form_validation->set_rules('cost','cost','xss_clean');
          $this->form_validation->set_rules('first_amount','first_amount','|xss_clean');
          $this->form_validation->set_rules('remarks','remarks','xss_clean');
          $this->form_validation->set_rules('status','status','xss_clean');
          $this->form_validation->set_rules('intakal_data','intakal_date','xss_clean');
          $this->form_validation->set_rules('saler_father_name','saler_father_name','required|xss_clean');
          $this->form_validation->set_rules('saler_address','saler_address','required|xss_clean');
          $this->form_validation->set_rules('pachaser_father_name','pachaser_father_name','required|xss_clean');
          $this->form_validation->set_rules('pachaser_address','pachaser_address','required|xss_clean');
          $this->form_validation->set_rules('s_no','s_no','required|xss_clean|is_unique[registry.sr_no]');
          $this->form_validation->set_rules('sq_feet','sq_feet','xss_clean');
          
          $this->form_validation->set_error_delimiters('<div class="alert alert-danger">','</div>');
          
          if($this->form_validation->run() !==false)
          {
             $data=array(
             'sr_no'=>$this->input->post('s_no'),
             'saler'=>$this->input->post('saler'), 
             'saler_father_name'=>$this->input->post('saler_father_name'),
             'saler_address'=>$this->input->post('saler_address'),
             'pachaser'=>$this->input->post('pachaser'), 
             'pachaser_father_name'=>$this->input->post('pachaser_father_name'), 
             'pachaser_address'=>$this->input->post('pachaser_address'), 
             'mobile'=>$this->input->post('mobile'), 
             'scheme'=>$this->input->post('scheme'), 
             'marlay'=>$this->input->post('marlay'), 
             'total_price'=>$this->input->post('total_price'), 
             'stamm'=>$this->input->post('stamm'), 
             'permition'=>$this->input->post('permition'), 
             'send'=>$this->input->post('send'), 
             'registration_number'=>$this->input->post('registration_number'), 
             'intakal'=>$this->input->post('intakal'), 
             'intakal_date'=>$this->input->post('intakal_data'), 
             'cost'=>$this->input->post('cost'), 
             'remarks'=>$this->input->post('remarks'), 
             'stam_submit_date'=>$this->input->post('stam_submit_data'), 
             'permition_submit_date'=>$this->input->post('permition_submit_data'), 
             'moza'=>$this->input->post('moza'), 
             'dcrate'=>$this->input->post('dcrate'), 
             'status'=>$this->input->post('status'),
             'date'=>date('Y-m-d'),
             'sq_feet'=>$this->input->post('sq_feet')
             );
             
             if(isset($_GET['c']))
             {
                $data['scheme_id']=$this->input->post('scheme_id');
                $data['khasra_id']=$this->input->post('khasra_id');
                $data['plot_id']=$this->input->post('plot_id');
                $data['iscompany']=1;
                
             }
             
             $this->db->trans_start();
             $this->db->insert('registry',$data);
             
             $famount=$this->input->post('first_amount');
             
             if(!empty($famount))
             {
                $data=array(
                'registry_ID'=>$this->db->insert_id(), 
                'amount'=>$this->input->post('first_amount'), 
                'type'=>'first_amount',
                'date'=>date('Y-m-d')
                );
             
               $this->db->insert('registry_received',$data);
             }
             
             $this->db->trans_complete();
             
             
             $error="<div class='alert alert-success'>Inserted.</div>";
             
             
          }
          
          
          
          
          
       }
       
       
       $schemes=$this->db->get('schemes')->result();
       
	   $this->load->view('admin/view_registry',array('error'=>$error,'schemes'=>$schemes));  
	}
    public function getData($sid=0)
    {
        $khasra="";
        $plots="";
        
        foreach($this->db->get_where('plots',array('scheme_id'=>$sid))->result() as $row)
        {
            $plots .='<option value="'.$row->id.'">'.$row->name.'</option>';
        }
        foreach($this->db->get_where('scheme_registry',array('scheme_id'=>$sid))->result() as $row)
        {
            $khasra .='<option value="'.$row->id.'">'.$row->name.'</option>';
        }
        
        $data=array(
        'plots'=>$plots,
        'khasra'=>$khasra
        );
        
        echo json_encode($data);
    }
 }