﻿<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_transfor extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
    public function index($pid,$sid)
	{
	   $error="";
        
        if($this->input->post('Submit'))
        {
            $this->form_validation->set_rules('name','Name','required|xss_clean');
            $this->form_validation->set_rules('fname','Father Name','required|xss_clean');
            $this->form_validation->set_rules('cnic','CNIC','required|xss_clean');
            $this->form_validation->set_rules('address','Address','required|xss_clean');
            $this->form_validation->set_rules('file_number','File Number','required|xss_clean');
            $this->form_validation->set_rules('mobile','Mobile','required|xss_clean');
            $this->form_validation->set_rules('marlay','Total Marlay','required|xss_clean');
            $this->form_validation->set_rules('feets','Square Feets','required|xss_clean');
            $this->form_validation->set_rules('rate_per_marla','Rate Per Marla','required|xss_clean');
            $this->form_validation->set_rules('first_amount','First Amount','required|xss_clean');
            $this->form_validation->set_rules('amount_status','Amount Status','required|xss_clean');
            $this->form_validation->set_rules('dealer','Dealer Name','required|xss_clean');
            $this->form_validation->set_rules('dealer_commision','Dealer Commision','required|xss_clean');
            $this->form_validation->set_rules('total_amount','Total Amount','required|xss_clean');
            $this->form_validation->set_rules('sdate','Start Date','required|xss_clean');
            $this->form_validation->set_rules('edate','End Date','required|xss_clean');
            $this->form_validation->set_rules('instalment_months','Instalment Months','required|xss_clean');
            $this->form_validation->set_rules('pid','Plote','required');
            
            $this->form_validation->set_error_delimiters('<div class="alert alert-danger">','</div>');
            
            if($this->form_validation->run() !== false)
            {
                
                    $img="";
                    $this->load->library('upload');
                    $this->upload->initialize($this->set_upload_options());
          
                    if($this->upload->do_upload('img'))
                    {
                        $data=$this->upload->data();
                        $img=$data['file_name'];
                    }
                
                
                $sdate=explode('/',$this->input->post('sdate'));
                $edate=explode('/',$this->input->post('edate'));
                $data=array(
                $pid, 
                $this->input->post('name'), 
                $this->input->post('fname'), 
                $this->input->post('cnic'), 
                $this->input->post('address'), 
                $this->input->post('mobile'), 
                $this->input->post('marlay'), 
                $this->input->post('total_amount'), 
                $this->input->post('first_amount'), 
                $this->input->post('dealer'), 
                $this->input->post('dealer_commision'), 
                $this->input->post('instalment_months'), 
                $this->input->post('feets'), 
                $this->input->post('rate_per_marla'), 
                $this->input->post('amount_status'), 
                $this->input->post('file_number'), 
                ($sdate[2].'-'.$sdate[0].'-'.$sdate[1]), 
                ($edate[2].'-'.$edate[0].'-'.$edate[1]),
                $img
                );
                
                $this->db->query('CALL sp_transfor(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);',$data);
    
                redirect(base_url("admin_detail/index/$pid/$sid"));    
                
                
            }
        }
       
        $s=$this->db->get_where('plots',array('id'=>$pid));
        foreach($s->result() as $plots){}
        
        $ss=$this->db->get_where('schemes',array('id'=>$sid));
        foreach($ss->result() as $schemes){}
        
        $u=$this->db->get_where('users',array('plot_id'=>intval($pid)));
        foreach($u->result() as $user){} 
         
       $total_received=0;
       $instalment=array();
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($pid),'user_cnic'=>$user->cnic)); 
       foreach($i->result() as $row)
       {
          $instalment[]=$row;
          $total_received += $row->received_instalment;
       }
       
       $p=$this->db->get_where('plots',array('id'=>intval($pid)));       
	   foreach($p->result() as $plote){}
       
       $s=$this->db->get_where('schemes',array('id'=>$plote->scheme_id));       
	   foreach($s->result() as $scheme){}
       
       
       
        $this->load->view('admin/view_transfor',array(
        'error'=>$error,
        'plots'=>$plots,
        'schemes'=>$schemes,
        'user'=>$user,
        'total_received'=>$total_received        
        ));
	}
    public function checkzero()
    {
        if($this->input->post('instalment_months')==0)
        {
            $_POST['instalment_months']=1;
        }
        return true;
    }
    private function set_upload_options()
    {   
       //  upload an image options
       $config = array();
       $config['upload_path'] = 'uploads';
       $config['allowed_types'] = 'gif|jpg|png';
       $config['overwrite']     = FALSE;
      
       return $config;
    }
}