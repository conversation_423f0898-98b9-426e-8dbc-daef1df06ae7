<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_scheme_registry extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
	public function index($id=0)
	{
	   $error="";
       
       if($this->input->post('submit'))
       {
          $this->form_validation->set_rules('name','Name','required|xss_clean');
          $this->form_validation->set_rules('khasra','Khasra','required|xss_clean');
          $this->form_validation->set_rules('marlay','Marlay','required|xss_clean');
          $this->form_validation->set_rules('sq_feet','Sq_Feet','required|xss_clean');
          
          if($this->form_validation->run() !== false)
          {
             $data=array(
             'scheme_id'=>$id,
             'name'=>$this->input->post('name'),
             'khasra'=>$this->input->post('khasra'),
             'marlay'=>$this->input->post('marlay'),
             'sq_feet'=>$this->input->post('sq_feet'),
             'date'=>date('Y-m-d')
             );
             
             
             $this->db->insert('scheme_registry',$data);
             
             if($this->db->affected_rows() > 0)
             {
                $error='<div class="alert alert-success">Inserted.</div>';
             }
             else
             {
                $error='<div class="alert alert-danger">Error. Try again</div>';
             }
             
          }
       }
       
       $reg=$this->db->get_where('scheme_registry',array('scheme_id'=>$id))->result();
       $scheme=$this->db->get_where('schemes',array('id'=>$id))->row();
       
	   $this->load->view('admin/view_scheme_registry',array('reg'=>$reg,'error'=>$error,'scheme'=>$scheme));
	}
	public function edit($id=0)
	{
	   $error="";
       
       if($this->input->post('submit'))
       {
          $this->form_validation->set_rules('name','Name','required|xss_clean');
          $this->form_validation->set_rules('khasra','Khasra','required|xss_clean');
          $this->form_validation->set_rules('marlay','Marlay','required|xss_clean');
          $this->form_validation->set_rules('sq_feet','Sq_Feet','required|xss_clean');
          
          if($this->form_validation->run() !== false)
          {
             $data=array(
             'name'=>$this->input->post('name'),
             'khasra'=>$this->input->post('khasra'),
             'marlay'=>$this->input->post('marlay'),
             'sq_feet'=>$this->input->post('sq_feet'),
             'date'=>date('Y-m-d')
             );
             
             $this->db->where('id',$id);
             $this->db->update('scheme_registry',$data);
             
             if($this->db->affected_rows() > 0)
             {
                $error='<div class="alert alert-success">Updated.</div>';
             }
             else
             {
                $error='<div class="alert alert-danger">Error. Try again</div>';
             }
             
          }
       }
       
       $data=$this->db->get_where('scheme_registry',array('id'=>$id))->row();
       $scheme=$this->db->get_where('schemes',array('id'=>$data->scheme_id))->row();
       
	   $this->load->view('admin/view_edit_scheme_registry',array('data'=>$data,'scheme'=>$scheme,'error'=>$error));
	}
    public function delete($id=0,$sid=0)
    {
        $this->db->where(array('id'=>intval($id)));
        $this->db->delete('scheme_registry');
        
        redirect(base_url("admin_scheme_registry/index/{$sid}"));
    }
    
 }