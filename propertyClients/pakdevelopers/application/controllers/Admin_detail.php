<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_detail extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
	public function index($id,$sid)
	{
	   $error="";
       
       $u=$this->db->get_where('users',array('plot_id'=>intval($id)));
       foreach($u->result() as $user){}
       
       if($u->num_rows()==0)
       {
         redirect(base_url('admin_plotes/index/'.$sid));
       }
       
       $p=$this->db->get_where('plots',array('id'=>intval($id)));       
	   foreach($p->result() as $plote){}
       
       $s=$this->db->get_where('schemes',array('id'=>$plote->scheme_id));       
	   foreach($s->result() as $scheme){}
       
       
       $total_received=0;
       $instalment=array();
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($id),'user_cnic'=>$user->cnic));  
       foreach($i->result() as $row)
       {
          $instalment[]=$row;
          $total_received += $row->received_instalment;
       }     
     
      //Testing
      
      $user->rate_per_marla=(!empty($user->rate_per_marla_t)) ? $user->rate_per_marla_t :$user->rate_per_marla;
      $user->first_amount=(float)$user->first_amount+(float)$user->transfor_amount;
      $user->total_amount=((($user->rate_per_marla/225)*$user->square_feets)+($user->marlay*$user->rate_per_marla));
      
      //End Testing
     
     
     
       
       $shortPayment = $this->shortPayment($id,$user->sdate,$user->edate,$user->total_amount,$user->first_amount,$user->instalment_months,$user->cnic);
       
       
       $this->db->order_by('id','desc');
       $transforUsers=$this->db->get_where('transfor',array('plot_id'=>intval($id)));
       
       $this->load->view('admin/view_detail',array(
       'error'=>$error,
       'user'=>$user,
       'plote'=>$plote,
       'scheme'=>$scheme,
       'instalment'=>$instalment,
       'total_received'=>$total_received,
       'shortPayment'=>$shortPayment,
       'shortMonth'=>$this->shortMonth($user->sdate,$user->edate),
       'transforUsers'=>$transforUsers,
       ));
	}
    public function shortMonth($sdate,$edate)
    {
        if($edate < date('Y-m-d'))
        {
           $sdate=explode('-',$sdate);
           $edate=explode('-',$edate);
        
           $a = ((($edate[0]-$sdate[0])-1)*12);
           $b = ($edate[1])+(12-$sdate[1]);
           
           return ($a+$b);   
        }
        else
        {
           $sdate=explode('-',$sdate);
                
           $a = (((date('Y')-$sdate[0])-1)*12);
           $b = (date('m'))+(12-$sdate[1]);
           
           return ($a+$b);        
        }
    }
    public function shortPayment($plote_id,$sdate,$edate,$total_amount,$first_amount,$instalment_months,$cnic)
    {
        
       $total_received=0;
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($plote_id),'user_cnic'=>$cnic));  
       foreach($i->result() as $row)
       {
          $total_received += $row->received_instalment;
       } 
        
       if($instalment_months==0) 
       {
          if($edate < date('Y-m-d'))
          {
            return ($total_amount-($total_received+$first_amount));
          }
          else
          {
            return 0;
          }
       }
        
        
        if($edate > date('Y-m-d'))
        {
            $sdate=explode('-',$sdate);
            $a = (((date('Y')-$sdate[0])-1)*12);
            $b = (date('m'))+(12-$sdate[1]);
        }
        else
        {
            $sdate=explode('-',$sdate);
            $edate=explode('-',$edate);
            $a = ((($edate[0]-$sdate[0])-1)*12);
            $b = ($edate[1])+(12-$sdate[1]);
        }
        
        $shortPayment = ($a+$b)
       *(($total_amount-$first_amount)/$instalment_months)
       +$first_amount-$total_amount
       +($total_amount-($total_received+$first_amount));
       
       return $shortPayment;
    }
    public function file($uid=0,$pid=0,$sid=0)
    {
        $this->db->where('id',$uid);
        $this->db->update('users',array('file'=>1));
        redirect(base_url("admin_detail/index/{$pid}/$sid"));
    }
    public function qslip($uid=0,$pid=0,$sid=0)
    {
        $this->db->where('id',$uid);
        $this->db->update('users',array('q_slip'=>1));
        redirect(base_url("admin_detail/index/{$pid}/$sid"));
    }
}