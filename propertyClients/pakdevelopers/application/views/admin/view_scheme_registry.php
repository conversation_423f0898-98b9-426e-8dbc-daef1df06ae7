<?php $this->load->view("admin/view_header_plotes");?>

            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?>
                    </center>
                </section> 
<script>
$(document).ready(function(){
  $("#testttt").click(function(){
    $('#sliderrrrr').slideToggle();
  });
});
</script> 
    <section class="content-header">
                    <h1 style="cursor: pointer;" id="testttt">
                        Khasra
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section class="content" id="sliderrrrr">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-12" role="form">
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Name:</label>
         <input name="name" required="" onkeyup="document.getElementById('error').remove();" class="form-control" value="<?=set_value('name');?>" required="" placeholder="Enter Name" type="text"/>
         <?=form_error('name');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Khasra:</label>
         <input name="khasra" required="" class="form-control" value="<?=set_value('khasra');?>" required="" placeholder="Enter Khasra" type="text"/>
         <?=form_error('khasra');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Marlay:</label>
         <input name="marlay" required="" class="form-control" value="<?=set_value('marlay');?>" required="" placeholder="Enter marlay" type="text"/>
         <?=form_error('marlay');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>SQ_Feet:</label>
         <input name="sq_feet" required="" class="form-control" value="<?=set_value('sq_feet');?>" required="" placeholder="Enter sq_feet" type="text"/>
         <?=form_error('sq_feet');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
          <input name="submit" value="Submit"  class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>

    <section class="content-header">
                    <h1>
                        Detail
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section>
<?php foreach($reg as $rr){
$Totalmarlay=$this->db->query('SELECT sum(marlay) as sum FROM `registry` WHERE khasra_id=?',array($rr->id))->row();    
$Totalfeets=$this->db->query('SELECT sum(sq_feet) as sum FROM `registry` WHERE khasra_id=?',array($rr->id))->row();

$totalFeets=((($rr->marlay*225)+$rr->sq_feet)-(($Totalmarlay->sum*225)+$Totalfeets->sum));


//$fm=intval($Totalfeets->sum/225);

//$Totalfeets->sum=$Totalfeets->sum-$fm;
//$Totalmarlay->sum = $Totalmarlay->sum+$fm;

?>
<table class="table table-bordered">
<tr>
  <th>Name</th>
  <th>Khasra</th>
  <th>Total Marlay</th>
  <th>Total Sq_Feet</th>
  <th>Remaining Marlay</th>
  <th>Remaining Sq_Feet</th>
  <th>Date</th>
  <th>Action</th>
</tr>
<tr style="  background-color: #B1A4D7;font-size: 37px;">
  <td><?=$rr->name;?></td>
  <td><?=$rr->khasra;?></td>
  <td><?=$rr->marlay;?></td>
  <td><?=$rr->sq_feet;?></td>
  <td><?=intval($totalFeets/225);?></td>
  <td><?=($totalFeets-(intval($totalFeets/225)*225));?></td>
  <td><?=$rr->date;?></td>
  <td>
  <a href="<?=base_url('admin_scheme_registry/edit/'.$rr->id);?>" class="btn btn-success">Edit</a>
  <a class="btn btn-danger" onclick="return confirm('press ok for delete this.');" href="<?=base_url('admin_scheme_registry/delete/'.$rr->id.'/'.$scheme->id);?>">DELETE</a>
</tr>
</table>
<table class="table table-bordered">
<tr>
   <th>Sr_No:</th>
   <th>Date:</th>
   <th>Saler:</th>
   <th>Pachaser:</th>
   <th>Scheme:</th>
   <th>Area:</th>
   <th>Sq Feet</th>
   <th>Chalan Submit:</th>
   <th>Permition Send:</th>
   <th>Permition Clear:</th>
   <th>Coat:</th>
   <th>Registration Number</th>
   <th>Intakal Send</th>
   <th>Intakal:</th>
   <th>cost:</th>
   <th>Received:</th>
   <th>Remaining:</th>
   <th>Status:</th>
   <th>Action:</th>
   
</tr>
<?php 
$rdata=$this->db->get_where('registry',array('khasra_id'=>$rr->id,'scheme_id'=>$scheme->id))->result();
foreach($rdata as $row){

$this->db->select("sum(amount) as sum");
$this->db->from('registry_received');
$this->db->where('registry_ID',$row->id);
$r=$this->db->get()->row();
?>
<tr>
   <td><?=$row->sr_no;?></td>
   <td><?=$row->date;?></td>
   <td><?=$row->saler;?></td> 
   <td><?=$row->pachaser;?></td>
   <td><?=$row->scheme;?></td>
   <td><?=$row->marlay;?></td>
   <td><?=$row->sq_feet;?></td>
   <td><?=$row->stam_submit_date;?></td>
   <td><?=$row->permition_submit_date;?></td>
   <td><?=$row->permition;?></td>
   <td><?=$row->send;?></td>
   <td><?=$row->registration_number;?></td>
   <td><?=$row->intakal_date;?></td>
   <td><?=$row->intakal;?></td>
   <td><?=$row->cost;?></td>
   <td><?=$r->sum;?></td>
   <td><?=($row->cost-$r->sum);?></td>
   <td><?=$row->status;?></td> 
   <td>
   <a href="<?=base_url('admin_all_registry/edit/'.$row->id);?>" class="btn btn-success">Edit</a>
   <a href="<?=base_url('admin_all_registry/delete/'.$row->id);?>" onclick="confirm('press ok for delete this.');" class="btn btn-danger">Delete</a>
   </td>
</tr>
<?php }?>
<tr style="background-color: #B1A4D7;">
    <td colspan="5"></td>
    <td><?=$Totalmarlay->sum;?></td>
    <td><?=$Totalfeets->sum;?></td>
    <td colspan="12"></td>
</tr>
</table>
<?php }?>

</section>
            </aside>
        <?php $this->load->view("admin/view_footer");?>