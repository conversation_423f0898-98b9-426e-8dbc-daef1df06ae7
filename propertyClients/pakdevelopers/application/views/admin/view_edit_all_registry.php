<?php $this->load->view("admin/view_header",array('url'=>'admin_plotes/index/'));
$this->db->select("amount as sum");
$this->db->from('registry_received');
$this->db->where(array('registry_ID'=>$data->id,'type'=>'first_amount'));
$r=$this->db->get()->row();

?>
<script>
function getData(str) {
    if (str == "") {
        document.getElementById("plot").innerHTML = "<option value=''>Sclect plot</option>";
        document.getElementById("khasra").innerHTML = "<option value=''>Sclect Khasra</option>";
        return;
    } else { 
        
        $.ajax({url: "<?=base_url('admin_registry/getData')?>/"+str, success: function(result){
            //$("#div1").html(result);
            obj = JSON.parse(result);
            
            $("#plot").html('<option value="">Sclect Plot</option>'+obj.plots);
            $("#khasra").html('<option value="">Sclect Khasra</option>'+obj.khasra);
        }});
    }
}
</script>

            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                <section class="content-header">
                    <h1 name="showdate" runat="server">
                        
                        asd
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>

<section class="content">
<div class="container col-sm-12">
<?=$error;?>
      <form method="post" action="" enctype="multipart/form-data">
      <div class="form-group col-sm-3">
          <label>Sr_No:</label>
            <input readonly="" type="text" value="<?=$data->sr_no;?>" name="s_no" class="form-control" />
            <?=form_error('s_no');?>
        </div>
 <?php if($data->iscompany==1){?>       
        <div class="form-group col-sm-3">
          <label>Select Scheme:</label>
            <select required="" onchange="getData(this.value);" name="scheme_id" class="form-control" >
            <option value="">Sclect Scheme</option>
               <?php foreach($schemes as $row){?>
                  <option value="<?=$row->id;?>" <?=($data->scheme_id==$row->id)?'selected=""':"";?>><?=$row->name;?></option>
               <?php }?>
            </select>
        </div>
        <div class="form-group col-sm-3">
          <label>Select Khasra:</label>
            <select required="" id="khasra" name="khasra_id" class="form-control" >
                  <option value="">Sclect Khasra</option>
                  <?php foreach($khasra as $row){?>
                  <option value="<?=$row->id;?>" <?=($data->khasra_id==$row->id)?'selected=""':"";?>><?=$row->name;?></option>
                  <?php }?>
            </select>
        </div>
        <div class="form-group col-sm-3">
          <label>Select Plot:</label>
            <select required="" id="plot" name="plot_id" class="form-control" >
               <option value="">Select Plot</option>
               <?php foreach($plots as $row){?>
                  <option value="<?=$row->id;?>" <?=($data->plot_id==$row->id)?'selected=""':"";?>><?=$row->name;?></option>
               <?php }?>
            </select>
        </div>
 <?php }?>      
      
        <div class="form-group col-sm-3">
          <label>Saler:</label>
            <input type="text" value="<?=$data->saler;?>" name="saler" class="form-control" />
            <?=form_error('saler');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Saler Father Name:</label>
            <input type="text" value="<?=$data->saler_father_name;?>" name="saler_father_name" class="form-control" />
            <?=form_error('saler_father_name');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Saler Address:</label>
            <input type="text" value="<?=$data->saler_address;?>" name="saler_address" class="form-control" />
            <?=form_error('saler_address');?>
        </div>
        
          <div class="form-group col-sm-3">
          <label>Pachaser:</label>
            <input type="text" value="<?=$data->pachaser;?>" name="pachaser" class="form-control" />
            <?=form_error('pachaser');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Pachaser Father Name:</label>
            <input type="text" value="<?=$data->pachaser_father_name;?>" name="pachaser_father_name" class="form-control" />
            <?=form_error('pachaser_father_name');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Pachaser Address:</label>
            <input type="text" value="<?=$data->pachaser_address;?>" name="pachaser_address" class="form-control" />
            <?=form_error('pachaser_address');?>
        </div>
        
          <div class="form-group col-sm-3">
          <label>Mobile:</label>
            <input type="text" value="<?=$data->mobile;?>" name="mobile" class="form-control" />
            <?=form_error('mobile');?>
        </div>
          <div class="form-group  col-sm-3">
          <label>Scheme:</label>
            <input type="text" value="<?=$data->scheme;?>" name="scheme" class="form-control" />
            <?=form_error('scheme');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Moza:</label>
            <input type="text" value="<?=$data->moza;?>" name="moza" class="form-control" />
            <?=form_error('moza');?>
        </div>
        <div class="form-group col-sm-3">
          <label>DC Rate:</label>
            <input type="text" value="<?=$data->dcrate;?>" name="dcrate" class="form-control" />
            <?=form_error('dcrate');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Marlay:</label>
            <input type="text" value="<?=$data->marlay;?>" name="marlay" class="form-control" />
            <?=form_error('marlay');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Sq Feet:</label>
            <input type="text" value="<?=$data->sq_feet;?>" name="sq_feet" class="form-control" />
            <?=form_error('sq_feet');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Total Price:</label>
            <input type="text" value="<?=$data->total_price;?>" name="total_price" class="form-control" />
            <?=form_error('total_price');?>
        </div>      
          <div class="form-group col-sm-3">
<script>
$(function() {
$( "#stam_submit_data" ).datepicker({format: 'yyyy-mm-dd'});
});
</script>
          <label>Stam Submit Date:</label>
            <input type="text" name="stam_submit_data" id="stam_submit_data" value="<?=($data->stam_submit_date=='0000-00-00')?'':$data->stam_submit_date;?>"  class="form-control" />
            <?=form_error('stam_submit_data');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Stam:</label>
            <input type="text" step="any" value="<?=$data->stamm;?>"  name="stamm" class="form-control" />
            <?=form_error('stamm');?>
        </div>
        <div class="form-group col-sm-3">
<script>
$(function() {
$( "#permition_submit_data" ).datepicker({format: 'yyyy-mm-dd'});
});
</script>
          <label>Permition Submit Data:</label>
            <input type="text" value="<?=($data->permition_submit_date=='0000-00-00')?'':$data->permition_submit_date;?>" id="permition_submit_data"  name="permition_submit_data" class="form-control" />
            <?=form_error('permition_submit_data');?>
        </div>
          <div class="form-group col-sm-3">
<script>
$(function() {
$( "#permition" ).datepicker({format: 'yyyy-mm-dd'});
});
</script>
          <label>Permition:</label>
            <input type="text" step="any" value="<?=($data->permition=='0000-00-00')?'':$data->permition;?>" id="permition"  name="permition" class="form-control" />
            <?=form_error('permition');?>
        </div>
          <div class="form-group col-sm-3">
<script>
$(function() {
$( "#send" ).datepicker({format: 'yyyy-mm-dd'});
});
</script>
          <label>send:</label>
            <input type="text" step="any" value="<?=($data->send=='0000-00-00')?'':$data->send;?>" id="send" name="send" class="form-control" />
            <?=form_error('send');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Registration Number:</label>
            <input type="text" step="any" value="<?=$data->registration_number;?>" name="registration_number" class="form-control" />
            <?=form_error('registration_number');?>
        </div>
        <div class="form-group col-sm-3">
<script>
$(function() {
$( "#intakal_data" ).datepicker({format: 'yyyy-mm-dd'});
});
</script>
          <label>Intakal Data:</label>
            <input type="text" value="<?=($data->intakal_date=='0000-00-00')?'':$data->intakal_date;?>" id="intakal_data"  name="intakal_data" class="form-control" />
            <?=form_error('intakal_data');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Intakal:</label>
            <input value="<?=$data->intakal;?>" type="text" name="intakal" class="form-control" />
            <?=form_error('intakal');?>
        </div>
          <div class="form-group col-sm-3">
          <label>cost:</label>
            <input type="number" step="any" value="<?=$data->cost;?>" name="cost" class="form-control" />
            <?=form_error('cost');?>
        </div>
        <div class="form-group col-sm-3">
          <label>First Amount:</label>
            <input type="number" step="any" value="<?=(isset($r->sum)?$r->sum:"");?>" name="first_amount" class="form-control" />
            <?=form_error('first_amount');?>
        </div>
          <div class="form-group col-sm-3">
          <label>remarks:</label>
            <input type="text" value="<?=$data->remarks;?>"name="remarks" class="form-control" />
            <?=form_error('remarks');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Status:</label>
            <select name="status" class="form-control">
              <option <?=(($data->status=="Panding")?'selected=""':'');?>>Panding</option>
              <option  <?=(($data->status=="Clear")?'selected=""':'');?>>Clear</option>
            </select>
            <?=form_error('status');?>
        </div>
        <div class="form-group col-sm-6">
          <input type="submit" name="Submit" class="btn btn-default" value="Submit" />
        </div>
      </form>
    </div>

</section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>