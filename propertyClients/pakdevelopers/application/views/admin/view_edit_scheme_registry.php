<?php $this->load->view("admin/view_header_plotes");?>

            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?>
                    </center>
                </section> 
<script>
$(document).ready(function(){
  $("#testttt").click(function(){
    $('#sliderrrrr').slideToggle();
  });
});
</script> 
    <section class="content-header">
                    <h1 style="cursor: pointer;" id="testttt">
                        Khasra
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section class="content" id="sliderrrrr">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-12" role="form">
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Name:</label>
         <input name="name" required="" class="form-control" value="<?=$data->name;?>" required="" placeholder="Enter Name" type="text"/>
         <?=form_error('name');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Khasra:</label>
         <input name="khasra" required="" class="form-control" value="<?=$data->khasra;?>" required="" placeholder="Enter Khasra" type="text"/>
         <?=form_error('khasra');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Marlay:</label>
         <input name="marlay" required="" class="form-control" value="<?=$data->marlay;?>" required="" placeholder="Enter marlay" type="text"/>
         <?=form_error('marlay');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>SQ_Feet:</label>
         <input name="sq_feet" required="" class="form-control" value="<?=$data->sq_feet;?>" required="" placeholder="Enter sq_feet" type="text"/>
         <?=form_error('sq_feet');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
          <input name="submit" value="Submit"  class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>

            </aside>
        <?php $this->load->view("admin/view_footer");?>