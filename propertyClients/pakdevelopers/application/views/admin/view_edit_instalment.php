<?php $this->load->view("admin/view_header_plotes");?>
<script>
function cheque(str)
{
    if(str=='Cash')
    {
      $('#cheque').hide();  
      $('#cheque_number').val('');
    }
    else
    {
        $('#cheque').show();
    }
}
function active()
{
    $('#error').hide();
    $('#submit').removeAttr("disabled");
    
}
</script>
            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?><br />
                        <h2><?=$plote->name;?></h2>
                    </center>
                </section> 
<script>
$(document).ready(function(){
  $("#testttt").click(function(){
    $('#sliderrrrr').slideToggle();
  });
});
</script> 
    <section class="content-header">
                    <h1 style="cursor: pointer;" id="testttt">
                        Instalment
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section class="content" id="sliderrrrr">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-12" role="form">
   
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Slip Number:</label>
         <input name="slip_number" required="" class="form-control" value="<?=$instalment->slip_number;?>" required="" placeholder="Enter slip_number" type="text"/>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
         <label>Received Instalment:</label>
         <input name="received_amount" required="" class="form-control" value="<?=$instalment->received_instalment;?>" required="" placeholder="Enter received_amount" type="text"/>
      </div>
   </div>
   <div class="form-group col-sm-2">
      <div class="col-sm-11">
             <label>Amount Status:</label>
             <select onchange="cheque(this.value);" name="amount_status" class="form-control" >
                  <option <?php if($instalment->amount_status=='Cash'){echo 'selected=""';}?>>Cash</option>
                  <option <?php if($instalment->amount_status=='Cheque'){echo 'selected=""';}?>>Cheque</option>
                  <option <?php if($instalment->amount_status=='Cash+Cheque'){echo 'selected=""';}?>>Cash+Cheque</option>
              </select>
      </div>
   </div>
   <div id="cheque" <?php if(empty($instalment->cheque_number)){echo 'style="display:none"';}?> class="form-group col-sm-2">
      <div class="col-sm-11">
         <label>Cheque No:</label>
         <input id="cheque_number" name="cheque_number"  class="form-control" value="<?=$instalment->cheque_number;?>"  placeholder="Enter received_amount" type="text"/>
         <?=form_error('cheque_number');?>
      </div>
   </div>
   <div class="form-group col-sm-2">
      <div class="col-sm-11">
<script>
$(function() {
$( "#datepicker" ).datepicker();
});
</script>
         <label>Date:</label>
         <?php
         
         $sdate=explode('-',$instalment->date);
        ?>
         <input name="date" required="" id="datepicker" value="<?=($sdate[1].'/'.$sdate[2].'/'.$sdate[0]);?>" class="form-control" value="" required="" placeholder="MM/DD/YYYY" type="text"/>
      </div>
   </div><br /><br /><br /><br />
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
          <input name="submit" value="Submit"  class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>

            </aside>
        <?php $this->load->view("admin/view_footer");?>