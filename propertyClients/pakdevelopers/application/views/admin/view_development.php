<?php $this->load->view("admin/view_header",array('url'=>'admin_bank_statement/index/'));?>
<script>
function cheque(str)
{
    if(str=='Cash')
    {
      $('#cheque').hide();  
      $('#cheque_number').val('');
    }
    else
    {
        $('#cheque').show();
    }
}
function active()
{
    $('#error').hide();
    $('#submit').removeAttr("disabled");
    
}
</script>
<aside class="right-side">                
                <!-- Content Header (Page header) -->
                <section class="content-header">
                    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?><br />
                        Development
                    </center>
                <a href="<?=base_url('admin_land/index/'.$this->uri->segment(3));?>" class="btn btn-success">Land Owner</a>
                <a href="<?=base_url('admin_bank_statement/index/'.$this->uri->segment(3));?>" class="btn btn-success">Bank Statement</a>
                <a href="<?=base_url('admin_scheme_registry/index/'.$this->uri->segment(3));?>" class="btn btn-success">Registry</a>
                
                </section>
                </section>
<section class="content" id="sliderrrrr">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-12" role="form">
      <div class="form-group col-sm-2">
      <div class="col-sm-11">
<script>
$(function() {
$( "#datepicker" ).datepicker();
});
</script>
         <label>Date:</label>
         <input name="date"  id="datepicker" value="<?=set_value('date');?>" class="form-control" value=""  placeholder="MM/DD/YYYY" type="text"/>
         <?=form_error('date');?>
      </div>
   </div>
   <div class="form-group col-sm-4">
      <div class="col-sm-11">
      <label>Remarks:</label>
         <textarea name="remarks" onkeyup="active();"  style="width: 260px; height: 36px;" placeholder="Remarks" class="form-control"><?=set_value('remarks');?></textarea>
         <?=form_error('remarks');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
         <label>Amount:</label>
         <input name="amount"  class="form-control" value="<?=set_value('amount');?>"  placeholder="Enter received_amount" type="number" step="any"/>
         <?=form_error('amount');?>
      </div>
   </div>
   <div class="form-group col-sm-2">
      <div class="col-sm-11">
             <label>Amount Type:</label>
             <select name="amount_status" onchange="cheque(this.value);" class="form-control" >
                  <option <?=set_select('amount_status', 'Cash'); ?>>Cash</option>
                  <option <?=set_select('amount_status', 'Cheque'); ?>>Cheque</option>
                  <option <?=set_select('amount_status', 'Cash+Cheque'); ?>>Cash+Cheque</option>
              </select>
              <?=form_error('amount_status');?>
      </div>
   </div>
   <?php
   $ch=set_value('cheque_number');
   
   ?>
   <div id="cheque" <?php if(empty($ch)){echo 'style="display:none"';}?> class="form-group col-sm-2">
      <div class="col-sm-11">
         <label>Cheque No:</label>
         <input id="cheque_number" name="cheque_number"  class="form-control" value="<?=set_value('cheque_number');?>"  placeholder="Enter received_amount" type="text"/>
         <?=form_error('cheque_number');?>
      </div>
   </div>
   <div class="form-group col-sm-12">
      <div class="col-sm-11">
          <input name="submit" id="submit" value="Submit" <?=($error !="") ? 'disabled=""' : '';?> class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>

<section class="content-header">
                    <table class="table table-bordered">
                    <tr>
                      <th>Received Amount</th>
                      <th>Expences</th>
                      <th>Remaining Amount</th>
                    </tr>
                    <tr>
                      <td style="color: green;"><?=get_price($receivedAmount);?></td>
                      <td style="color: red;"><?=get_price(($sum+$landsum));?></td>
                      <td style="color: green;"><?=get_price($receivedAmount-($sum+$landsum));?></td>
                    </tr>
                    </table>
                </section>
<section class="content-header">
                    <table class="table table-bordered">
                    <tr>
                      <th colspan="2" style="text-align:center;font-size: 35px;">Total Area</th>
                      <th colspan="2" style="text-align:center;font-size: 35px;">Saled Area</th>
                      <th colspan="2" style="text-align:center;font-size: 35px;">Remaning Area</th>
                    </tr>
                    <tr>
                      <th style="text-align: center;">Marla</th>
                      <th style="text-align: center;">Feets</th>
                      <th style="text-align: center;">Marla</th>
                      <th style="text-align: center;">Feets</th>
                      <th style="text-align: center;">Marla</th>
                      <th style="text-align: center;">Feets</th>
                    </tr>
                    <tr>
                    <?php
                    $sTotalMarlay=(($scheme->marlay*225)+$scheme->feets)/225;
                    $sf=explode('.',$sTotalMarlay);
                    
                    ?>
                      <td style="text-align: center;"><?=$sf[0];?></td>
                      <td style="text-align: center;"><?php if(isset($sf[1])){echo ceil(('0.'.$sf[1])*225);}else{echo 0;}?></td>
                     
                      <?php $f=explode('.',$totalMrlays);?>
                      <td style="text-align: center;"><?=$f[0];?></td>
                      <td style="text-align: center;"><?php if(isset($f[1])){echo ceil(('0.'.$f[1])*225);}else{echo 0;}?></td>
                    
                      <?php $rf=explode('.',($sTotalMarlay-$totalMrlays));?>
                      <td style="text-align: center;"><?=$rf[0];?></td>
                      <td style="text-align: center;"><?php if(isset($rf[1])){echo ceil(('0.'.$rf[1])*225);}else{echo 0;}?></td>
                    </tr>
                    </table>
</section>

<section class="content-header">
                    <h1 class="alert btn-success">
                        Expence Detail 
                        <small>Control panel</small>
                    </h1>
                    
                </section>
    <section class="content-header">
        <table class="table table-bordered">
                        <tbody><tr>
                            <th>Date</th>
                            <th>Remarks</th>
                            <th>Amount</th>
                            <th>Type</th>
                            <th>Cheque Number</th>
                            <th style="width: 300px">Action</th>
                        </tr>
                 <?php foreach($query->result() as $row){?>
                        <tr>
                            <td><?=$row->date;?></td>
                            <td><?=$row->remarks;?></td>
                            <td><?=get_price($row->price);?></td>
                            <td><?=$row->amount_type;?></td>
                            <td><?=$row->cheque_number;?></td>
                            
                            <td>
                                <a onclick="return confirm('press ok for delete this.');" href="<?=base_url('admin_development/delete/'.$row->id.'/'.$this->uri->segment(3));?>" class="btn btn-danger">DELETE</a>
                                <a href="<?=base_url('admin_development/edit/'.$row->id.'/'.$this->uri->segment(3));?>" class="btn btn-success">Edit</a>
                            </td>
                        </tr>
                  <?php }?>
                        <tr style="background-color: #BCC5EB;">
                           <td></td>
                           <td>Grand Total</td>
                           <td><?=get_price($sum);?></td>
                           <td></td>
                           <td></td>
                           <td></td>
                        </tr>
                    </tbody></table>
                </section>
            </aside><!-- /.right-side -->


<?php $this->load->view("admin/view_footer");?>