<?php $this->load->view("admin/view_header",array('url'=>'admin_plotes/index/'));
$ci =& get_instance();

?>
<style>
td a
{
    color: green;
}

</style>
            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        Search Result
                    </center>
                </section>
    


<script>
$(document).ready(function(){
  
  $("#deletee").click(function(){
    $('.delete').toggle();
  });
  
  $("#edite").click(function(){
    $('.edit').toggle();
  });
  
  $("#instalmente").click(function(){
    $('.instalment').toggle();
  });
  
});

</script>
<style>
.delete,.edit,.instalment
{
    display: none;
}
</style>    
    <section class="content-header">
                    <h1>
                        All Search Plote 
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a class="btn btn-warning" id="instalmente"><i class=""></i> Instalment</a></li>
                        <li><a class="btn btn-danger" id="deletee"><i class=""></i> Delete</a></li>
                        <li><a class="btn btn-success" id="edite"><i class=""></i> Edit</a></li>
                    </ol>
                </section>
    <section class="content-header">
        <table class="table table-bordered">
                        <tbody><tr>
                            <th>Scheme Name</th>
                            <th>Plote No:</th>
                            <th>User Name</th>
                            <th>Marlay</th>
                            <th>SFT</th>
                            <th>Total Amount</th>
                            <th>Receved Amount</th>
                            <th>Balance</th>
                            <th>Short Payment</th>
                            <th>Commision</th>
                            <th class="instalment" style="width: 1px">Instalment</th>
                            <th class="delete" style="width: 1px">Delete</th>
                            <th class="edit" style="width: 1px">Edit</th>
                        </tr>
                   <?php foreach($query->result() as $row){
                            
                            $marlay = 0;
                            $square_feets = 0 ;
                            
                            
                            $this->db->where('plote_id',$row->plot_id);
                            $this->db->select_sum('received_instalment');
                            $query = $this->db->get('instalment');
                            
                            foreach($query->result() as $r){}
                            
                            $shortPayment=0;
                            if(!empty($row->total_amount))
                            $shortPayment=$ci->shortPayment($row->plot_id,$row->sdate,$row->edate,$row->total_amount,$row->first_amount,$row->instalment_months);
                            $marlay += $row->marlay;
                            $square_feets += $row->square_feets;
                            
                    
                    ?>     
                        <tr>
                            <td><?=$row->scheme_name;?></td>
                            <td><a style="width: 100%;height: 100%;" href="<?php if(empty($row->total_amount)){echo base_url('admin_users/index/'.$row->plot_id.'/'.$scheme->id);}else{echo '#';}?>"><?=$row->plote_name;?></a></td>
                            <td><?php
                            if(!empty($row->total_amount))
                            {
                                echo '<a style="width: 100%;height: 100%;" href="'.base_url('admin_detail/index/'.$row->plot_id.'/'.$row->sid).'">'.$row->name.'</a>';
                            }
                            else
                            {
                                echo $row->name;    
                            }
                            ?></td>
                            <td><?=$row->marlay;?></td>
                            <td><?=$row->square_feets;?></td>
                            <td><?=get_price(round($row->total_amount));?></td>
                            <td><?=get_price(round($r->received_instalment+$row->first_amount));?></td>
                            <td><?=get_price(round($row->total_amount-($r->received_instalment+$row->first_amount)));?></td>
                            <td><?=get_price(round($shortPayment));?></td>
                            <td><?=get_price(round($row->dealer_commision));?></td>
                            <td class="instalment">
                                <a href="<?php if(!empty($row->total_amount)){echo base_url('admin_instalment/index/'.$row->plot_id);}else{echo '#';}?>" class="btn btn-warning">Add Instalment</a>
                            </td>
                            <td class="delete">    
                                <a onclick="return confirm('Are you sure you want to delete <?=$row->plote_name; ?> number plote.');" href="<?=base_url('admin_plotes/delete/'.$row->plot_id.'/'.$row->sid);?>" class="btn btn-danger">DELETE</a>
                            </td>
                            <td class="edit">    
                                <a href="<?=base_url('admin_plotes/edit/'.$row->plot_id.'/'.$row->sid);?>" class="btn btn-success">Edit</a>
                            </td>
                        </tr>
                  <?php }?>
                    </tbody></table>
                </section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>