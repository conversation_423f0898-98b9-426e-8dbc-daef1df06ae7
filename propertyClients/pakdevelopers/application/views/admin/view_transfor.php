﻿<?php $this->load->view("admin/view_header_plotes",array('scheme'=>$schemes));?>
<script>
function readURL(input) {

    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            //$('#blah').attr('src', e.target.result);
            
            document.getElementById('blah').src=e.target.result;
            
        }

        reader.readAsDataURL(input.files[0]);
    }
}

</script>
            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
<section class="content-header">
<img id="blah" style="margin-bottom: -264px;width: 150px;" alt="" src="" /> 
                    <center  style="font-size: 80px;font-family: monospace;">
                        <?=$schemes->name;?><br />
                        <?=$plots->name;?> 
                    </center>
                </section>
    
    <section class="content-header">
                    <h1 name="showdate" runat="server">
                        
                        
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<script>

function gettotal()
{
   var marlay = document.getElementById('marlay').value;
   var rate_per_marla = document.getElementById('rate_per_marla').value;
   var feets = document.getElementById('feets').value;
      
   document.getElementById('total_amount').value=Math.round(((rate_per_marla/225)*feets)+(marlay*rate_per_marla));   
}
function getinstalment()
{
    //per_month_instalment
    var first_amount = document.getElementById('first_amount').value;
    var instalment_months = document.getElementById('instalment_months').value;
    var total_amount = document.getElementById('total_amount').value;

    document.getElementById('per_month_instalment').value=Math.round((total_amount-first_amount)/instalment_months);
}
function getfirstamount()
{
    var remaining = document.getElementById('remaining').value;
    var total_amount = document.getElementById('total_amount').value;

//alert(total_amount+"          "+remaining+"="+(total_amount-remaining));
    document.getElementById('first_amount').value=(total_amount-remaining);
}

function getdate()
{
    //alert('sdf');
    var sdate = document.getElementById('sdate').value;
    sdate = sdate.split("/");
    
    var edate = document.getElementById('edate').value;
    edate = edate.split("/");
    
    
    var a = (((edate[2]-sdate[2])-1)*12);
    var b = parseInt(edate[0])+parseInt(12-sdate[0]);
    //instalment_months
    document.getElementById('instalment_months').value=Math.round((parseInt(a)+parseInt(b)));
}//remaining

function check()
{
    
    gettotal();
    getinstalment();
    getdate();
    getfirstamount();
}

</script>
<?php  
$user->rate_per_marla=(!empty($user->rate_per_marla_t)) ? $user->rate_per_marla_t :$user->rate_per_marla;
$user->first_amount=(float)$user->first_amount+(float)$user->transfor_amount;
$user->total_amount=((($user->rate_per_marla/225)*$user->square_feets)+($user->marlay*$user->rate_per_marla));
?>
<section class="content">
<div class="container col-sm-12">
<?=$error;?><?=form_error('pid');?>
      <form method="post" action="" enctype="multipart/form-data">
      <input type="hidden" name="pid" value="<?=$plots->id;?>" />
        <div class="form-group col-sm-4">
          <label>Name:</label>
            <input type="text" value="<?=set_value('name');?>" name="name" class="form-control" />
            <?=form_error('name');?>
        </div>
          <div class="form-group col-sm-4">
          <label>Father Name:</label>
            <input type="text" value="<?=set_value('fname');?>" name="fname" class="form-control" />
            <?=form_error('fname');?>
        </div>
          <div class="form-group col-sm-4">
          <label>CNIC:</label>
            <input type="text" value="<?=set_value('cnic');?>" name="cnic" class="form-control" />
            <?=form_error('cnic');?>
        </div>
          <div class="form-group  col-sm-12">
          <label>Address:</label>
            <input type="text" value="<?=set_value('address');?>" name="address" class="form-control" />
            <?=form_error('address');?>
        </div>
          <div class="form-group col-sm-3">
          <label>File Number:</label>
            <input type="text" readonly="" value="Tr+<?=$user->file_number;?>" name="file_number" class="form-control" />
            <?=form_error('file_number');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Mobile:</label>
            <input type="text" value="<?=set_value('mobile');?>" name="mobile" class="form-control" />
            <?=form_error('mobile');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Totle Marlay:</label>
            <input type="number" step="any" readonly="" value="<?=$user->marlay;?>" id="marlay" onkeyup="check();" name="marlay" class="form-control" />
            <?=form_error('marlay');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Square Feets:</label>
            <input type="number" step="any" readonly="" value="<?=$user->square_feets;?>" id="feets" onkeyup="check();" name="feets" class="form-control" />
            <?=form_error('feets');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Rate Per Marla:</label>
            <input type="number" step="any" value="<?=set_value('rate_per_marla');?>" id="rate_per_marla" onkeyup="check();" name="rate_per_marla" class="form-control" />
            <?=form_error('rate_per_marla');?>
        </div>
          <div class="form-group col-sm-3">
          <label>First Amount:</label>
            <input type="number" step="any" readonly="" value="<?=set_value('first_amount');?>" id="first_amount" onkeyup="check();" name="first_amount" class="form-control" />
            <?=form_error('first_amount');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Amount Status:</label>
              <select name="amount_status" class="form-control" >
                  <option>Cash</option>
                  <option>Cheque</option>
                  <option>Cash+Cheque</option>
              </select>
        </div>
        <div class="form-group col-sm-3">
          <label>Per Month Instalment:</label>
            <input value="<?=(intval(set_value('instalment_months'))==0) ? 0 : get_price(round(((float)set_value('total_amount')-(float)set_value('first_amount'))/(float)set_value('instalment_months')));?>" type="text" id="per_month_instalment" readonly="" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Dealer Name:</label>
            <input value="<?=$user->dealer;?>" readonly="" type="text" name="dealer" class="form-control" />
            <?=form_error('dealer');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Dealer Commision:</label>
            <input type="number" step="any" readonly="" value="<?=$user->dealer_commision;?>" name="dealer_commision" class="form-control" />
            <?=form_error('dealer_commision');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Total Amount:</label>
            <input type="text" value="<?=set_value('total_amount');?>" id="total_amount" readonly="" name="total_amount" class="form-control" />
            <?=form_error('total_amount');?>
        </div>
        <div class="form-group col-sm-3">
          <label>Remaining Balance:</label>
            <input type="text" readonly="" id="remaining" value="<?=($user->total_amount-($total_received+$user->first_amount));?>" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
<script>
$(function() {
$( "#sdate" ).datepicker();
});
</script>
          <label>Start Date:</label>
            <input type="text" name="sdate" id="sdate" onchange="check();" value="<?=set_value('sdate');?>"  class="form-control" />
            <?=form_error('sdate');?>
        </div>
        <div class="form-group col-sm-3">
<script>
$(function() {
$( "#edate" ).datepicker();
});
</script>
          <label>End Date:</label>
            <input type="text" value="<?=set_value('edate');?>" id="edate" onchange="check();" name="edate" class="form-control" />
            <?=form_error('edate');?>
        </div>
          <div class="form-group col-sm-3">
          <label>Instalment Months:</label>
            <input type="text" value="<?=set_value('instalment_months');?>" readonly="" onchange="check();" id="instalment_months" name="instalment_months" class="form-control" />
            <?=form_error('instalment_months');?>
        </div>
        <div class="form-group col-sm-2">
          <label>User image:</label>
            <input type="file" onchange="readURL(this);" name="img" class="btn btn-success" class="form-control" />
        </div>
        <div class="form-group col-sm-6">
          <input type="submit" name="Submit" class="btn btn-default" value="Submit" />
        </div>
      </form>
    </div>

</section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>