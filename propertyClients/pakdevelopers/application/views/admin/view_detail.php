<?php $this->load->view("admin/view_header_plotes",array('scheme'=>$scheme));
$CI =& get_instance();
?>

            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
<?php
if(file_exists(base_url('uploads/'.$user->image)))
{
?>
    <img style="margin-bottom: -264px;width: 150px;" alt="" src="<?=base_url('uploads/'.$user->image);?>" />
<?php }?> 
                    <center  style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?><br />
                        <?=$plote->name;?>
                    </center>
                </section> 

<script>
$(document).ready(function(){
  $("#testttt").click(function(){
    $('#sliderrrrr').slideToggle();
  });
});
</script> 

    <section class="content-header">
                    <h1  style="background-color: #76DA76;" class="alert btn-success">
                        User Information
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a class="btn btn-success" onclick="return confirm('press ok for add Q File.');" href="<?=base_url('admin_detail/qslip/'.$user->id.'/'.$plote->id.'/'.$scheme->id);?>"><i></i> Q slip</a></li>
                        <li><a class="btn btn-success" onclick="return confirm('press ok for add File.');" href="<?=base_url('admin_detail/file/'.$user->id.'/'.$plote->id.'/'.$scheme->id);?>"><i></i> File</a></li>
                        
                        <li><a class="btn btn-success" href="<?=base_url('admin_instalment/index/'.$plote->id);?>"><i></i> Instalmente</a></li>
                        <li><a class="btn btn-success" href="<?=base_url('admin_transfor/index/'.$plote->id.'/'.$scheme->id);?>"><i></i> Transfor</a></li>
                        <li><a class="btn btn-success" href="<?=base_url('admin_edit_user/index/'.$plote->id.'/'.$scheme->id);?>"><i></i> Edit</a></li>
                    </ol>
                </section>
<section class="content">
<div class="container col-sm-12">

        <div class="form-group col-sm-4">
          <label>Name:</label>
            <input type="text" value="<?=$user->name;?>" readonly="" name="name" class="form-control" />
        </div>
          <div class="form-group col-sm-4">
          <label>Father Name:</label>
            <input type="text" readonly="" value="<?=$user->fname;?>" name="fname" class="form-control" />
        </div>
          <div class="form-group col-sm-4">
          <label>CNIC:</label>
            <input type="text" readonly="" value="<?=$user->cnic;?>" name="cnic" class="form-control" />
        </div>
          <div class="form-group  col-sm-12">
          <label>Address:</label>
            <input type="text" readonly="" value="<?=$user->address;?>" name="address" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>File Number:</label>
            <input type="text" readonly="" value="<?=$user->file_number;?>" name="file_number" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Mobile:</label>
            <input type="text" readonly="" value="<?=$user->mobile;?>" name="mobile" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Totle Marlay:</label>
            <input type="text" readonly="" value="<?=$user->marlay;?>" name="marlay" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Square Feets:</label>
            <input type="text" readonly="" value="<?=$user->square_feets;?>" name="feets" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Rate Per Marla:</label>
            <input type="text" value="<?=get_price($user->rate_per_marla);?>" readonly="" name="rate_per_marla" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>First Amount:</label>
            <input type="text" readonly="" value="<?=get_price($user->first_amount);?>" name="first_amount" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Amount Status:</label>
              <input type="text" readonly="" value="<?=$user->amount_status;?>" name="first_amount" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Instalment Months:</label>
            <input type="text" readonly="" value="<?=$user->instalment_months;?>" name="instalment_months" class="form-control" />
        </div>
        
        
          <div class="form-group col-sm-3">
          <label>Total Amount:</label>
            <input type="text" value="<?=get_price($user->total_amount);?>" readonly="" name="total_amount" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Total Received Amount:</label>
            <input type="text" readonly="" value="<?=get_price($total_received+$user->first_amount);?>" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Remaining Balance:</label>
            <input type="text" readonly="" value="<?=get_price($user->total_amount-($total_received+$user->first_amount));?>" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Per Month Instalment:</label>
            <input type="text" readonly="" value="<?=($user->instalment_months==0) ? 0 : get_price(round(($user->total_amount-$user->first_amount)/$user->instalment_months));?>" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Start Date:</label>
            <input type="text" name="sdate" readonly="" value="<?=$user->sdate;?>" id="datepicker" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>End Date:</label>
            <input type="text" value="<?=$user->edate;?>" readonly="" name="edate" id="datepicker1" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Months Now A Day:</label>
            <input type="text" value="<?=$shortMonth;?>" readonly="" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Short Payment:</label>
            <input type="text" value="<?=get_price(round($shortPayment));?>" readonly="" class="form-control" />
        </div>
    </div>

</section>
    <section class="content-header">
                    <h1  class="alert btn-primary">
                        Instalment Detail
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section>
<table class="table table-bordered">
<tr>
  <th>Slip Number</th>
  <th>Received Instalmen</th>
  <th>Amount Status</th>
  <th>Date</th>
</tr>
<?php foreach($instalment as $row){?>
<tr>
  <td><?=$row->slip_number;?></td>
  <td><?=get_price($row->received_instalment);?></td>
  <td><?=$row->amount_status;?></td>
  <td><?=$row->date;?></td>
</tr>
<?php }?>
</table>

</section>



























<?php foreach($transforUsers->result() as $user){
    
       $total_received=0;
       $instalment=array();
       $i=$this->db->get_where('instalment',array('plote_id'=>$user->plot_id,'user_cnic'=>$user->cnic));  
       foreach($i->result() as $row)
       {
          $instalment[]=$row;
          $total_received += $row->received_instalment;
       } 

//Testing
$user->rate_per_marla=(!empty($user->rate_per_marla_t)) ? $user->rate_per_marla_t :$user->rate_per_marla;
$user->first_amount=(float)$user->first_amount+(float)$user->transfor_amount;
$user->total_amount=((($user->rate_per_marla/225)*$user->square_feets)+($user->marlay*$user->rate_per_marla));
//End Testing

$shortMonth=$CI->shortMonth($user->sdate,$user->edate);
$shortPayment = $CI->shortPayment($user->plot_id,$user->sdate,$user->edate,$user->total_amount,$user->first_amount,$user->instalment_months,$user->cnic);
?>
<section class="content-header">
                    <h1  style="background-color: #76DA76;" class="alert btn-success">
                        User Information
                        <small>Control panel</small>
                    </h1>
                </section>
<section class="content">
<section class="content">
<div class="container col-sm-12">

        <div class="form-group col-sm-4">
          <label>Name:</label>
            <input type="text" value="<?=$user->name;?>" readonly="" name="name" class="form-control" />
        </div>
          <div class="form-group col-sm-4">
          <label>Father Name:</label>
            <input type="text" readonly="" value="<?=$user->fname;?>" name="fname" class="form-control" />
        </div>
          <div class="form-group col-sm-4">
          <label>CNIC:</label>
            <input type="text" readonly="" value="<?=$user->cnic;?>" name="cnic" class="form-control" />
        </div>
          <div class="form-group  col-sm-12">
          <label>Address:</label>
            <input type="text" readonly="" value="<?=$user->address;?>" name="address" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>File Number:</label>
            <input type="text" readonly="" value="<?=$user->file_number;?>" name="file_number" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Mobile:</label>
            <input type="text" readonly="" value="<?=$user->mobile;?>" name="mobile" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Totle Marlay:</label>
            <input type="text" readonly="" value="<?=$user->marlay;?>" name="marlay" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Square Feets:</label>
            <input type="text" readonly="" value="<?=$user->square_feets;?>" name="feets" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Rate Per Marla:</label>
            <input type="text" value="<?=get_price($user->rate_per_marla);?>" readonly="" name="rate_per_marla" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>First Amount:</label>
            <input type="text" readonly="" value="<?=get_price($user->first_amount);?>" name="first_amount" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Amount Status:</label>
              <input type="text" readonly="" value="<?=$user->amount_status;?>" name="first_amount" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Instalment Months:</label>
            <input type="text" readonly="" value="<?=$user->instalment_months;?>" name="instalment_months" class="form-control" />
        </div>
        
        
          <div class="form-group col-sm-3">
          <label>Total Amount:</label>
            <input type="text" value="<?=get_price($user->total_amount);?>" readonly="" name="total_amount" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Total Received Amount:</label>
            <input type="text" readonly="" value="<?=get_price($total_received+$user->first_amount);?>" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Remaining Balance:</label>
            <input type="text" readonly="" value="<?=get_price($user->total_amount-($total_received+$user->first_amount));?>" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Per Month Instalment:</label>
            <input type="text" readonly="" value="<?=($user->instalment_months==0) ? 0 : get_price(round(($user->total_amount-$user->first_amount)/$user->instalment_months));?>" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Start Date:</label>
            <input type="text" name="sdate" readonly="" value="<?=$user->sdate;?>" id="datepicker" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>End Date:</label>
            <input type="text" value="<?=$user->edate;?>" readonly="" name="edate" id="datepicker1" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Months Now A Day:</label>
            <input type="text" value="<?=$shortMonth;?>" readonly="" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Short Payment:</label>
            <input type="text" value="<?=get_price(round($shortPayment));?>" readonly="" class="form-control" />
        </div>
    </div>

</section>
    <section class="content-header">
                    <h1 class="alert btn-primary">
                        Instalment Detail
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section>
<table class="table table-bordered">
<tr>
  <th>Slip Number</th>
  <th>Received Instalmen</th>
  <th>Amount Status</th>
  <th>Date</th>
</tr>
<?php foreach($instalment as $row){?>
<tr>
  <td><?=$row->slip_number;?></td>
  <td><?=get_price($row->received_instalment);?></td>
  <td><?=$row->amount_status;?></td>
  <td><?=$row->date;?></td>
</tr>
<?php }?>
</table>

</section>
<?php }?>






































            </aside>
        <?php $this->load->view("admin/view_footer");?>