<?php $this->load->view("admin/view_header",array('url'=>'admin_bank_statement/index/'));?>
<script>
function cheque(str)
{
    if(str=='Cash')
    {
      $('#cheque').hide();  
      $('#cheque_number').val('');
    }
    else
    {
        $('#cheque').show();
    }
}
function active()
{
    $('#error').hide();
    $('#submit').removeAttr("disabled");
    
}
</script>
<aside class="right-side">                
                <!-- Content Header (Page header) -->
                <section class="content-header">
                    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?><br />
                        Bank Statement
                    </center>
                <a href="<?=base_url('admin_development/index/'.$this->uri->segment(3));?>" class="btn btn-success">Back</a>
                </section>
                </section>
<section class="content" id="sliderrrrr">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-12" role="form">
      <div class="form-group col-sm-2">
      <div class="col-sm-11">
<script>
$(function() {
$( "#datepicker" ).datepicker();
});
</script>
         <label>Date:</label>
         <input name="date"  id="datepicker" value="<?=set_value('date');?>" class="form-control" value=""  placeholder="MM/DD/YYYY" type="text"/>
         <?=form_error('date');?>
      </div>
   </div>
   <div class="form-group col-sm-4">
      <div class="col-sm-11">
      <label>Slip No:</label>
         <textarea name="remarks" onkeyup="active();"  style="width: 260px; height: 36px;" placeholder="Slip No" class="form-control"><?=set_value('remarks');?></textarea>
         <?=form_error('remarks');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
         <label>Amount:</label>
         <input name="amount"  class="form-control" value="<?=set_value('amount');?>"  placeholder="Enter received_amount" type="number" step="any"/>
         <?=form_error('amount');?>
      </div>
   </div>
   <div class="form-group col-sm-2">
      <div class="col-sm-11">
             <label>Amount Type:</label>
             <select name="amount_status" onchange="cheque(this.value);" class="form-control" >
                  <option <?=set_select('amount_status', 'Cash'); ?>>Cash</option>
                  <option <?=set_select('amount_status', 'Cheque'); ?>>Cheque</option>
                  <option <?=set_select('amount_status', 'Cash+Cheque'); ?>>Cash+Cheque</option>
              </select>
              <?=form_error('amount_status');?>
      </div>
   </div>
   <?php
   $ch=set_value('cheque_number');
   
   ?>
   <div id="cheque" <?php if(empty($ch)){echo 'style="display:none"';}?> class="form-group col-sm-2">
      <div class="col-sm-11">
         <label>Cheque No:</label>
         <input id="cheque_number" name="cheque_number"  class="form-control" value="<?=set_value('cheque_number');?>"  placeholder="Enter received_amount" type="text"/>
         <?=form_error('cheque_number');?>
      </div>
   </div>
   <div class="form-group col-sm-12">
      <div class="col-sm-11">
          <input name="submit" id="submit" value="Submit" <?=($error !="") ? 'disabled=""' : '';?> class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>

<section class="content-header">
                    <table class="table table-bordered">
                    <tr>
                      <th>Total Received Amount</th>
                      <th>Commision</th>
                      <th>Development Expences</th>
                      <th>Land Paid</th>
                      <th>Bank Statement</th>
                      <th>Bank Slip Balance</th>
                      <th>Difference</th>
                    </tr>
                    <tr>
                      <td style="color: green;"><?=get_price($receivedAmount);?></td>
                      <td><?=get_price($commision);?></td>
                      <td style="color: red;"><?=get_price($dpSum);?></td>
                      <td><?=get_price($land_paid-$scheme->first_amount);?></td>
                      <td style="color: green;"><?=get_price((($receivedAmount)-($landsum+$dpSum)+$scheme->first_amount)-$commision);?></td>
                      <td style="color: red;"><?=get_price(($sum));?></td>
                      <td style="color: green;"><?=get_price(($receivedAmount-($sum+$dpSum+$commision)));//get_price((($receivedAmount)-($landsum+$dpSum)+$scheme->first_amount)-($sum));?></td>
                    </tr>
                    </table>
                </section>


<section class="content-header">
                    <h1 class="alert btn-success">
                        Expence Detail 
                        <small>Control panel</small>
                    </h1>
                    
                </section>
    <section class="content-header">
        <table class="table table-bordered">
                        <tbody><tr>
                            <th>Date</th>
                            <th>Slip No</th>
                            <th>Amount</th>
                            <th>Type</th>
                            <th>Cheque Number</th>
                            <th style="width: 300px">Action</th>
                        </tr>
                 <?php foreach($query->result() as $row){?>
                        <tr>
                            <td><?=$row->date;?></td>
                            <td><?=$row->remarks;?></td>
                            <td><?=get_price($row->price);?></td>
                            <td><?=$row->amount_type;?></td>
                            <td><?=$row->cheque_number;?></td>
                            
                            <td>
                                <a onclick="return confirm('press ok for delete this.');" href="<?=base_url('admin_bank_statement/delete/'.$row->id.'/'.$this->uri->segment(3));?>" class="btn btn-danger">DELETE</a>
                                <a href="<?=base_url('admin_bank_statement/edit/'.$row->id.'/'.$this->uri->segment(3));?>" class="btn btn-success">Edit</a>
                            </td>
                        </tr>
                  <?php }?>
                        <tr style="background-color: #BCC5EB;">
                           <td></td>
                           <td>Grand Total</td>
                           <td><?=get_price($sum);?></td>
                           <td></td>
                           <td></td>
                           <td></td>
                        </tr>
                    </tbody></table>
                </section>
            </aside><!-- /.right-side -->


<?php $this->load->view("admin/view_footer");?>