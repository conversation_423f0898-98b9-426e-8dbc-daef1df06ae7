<?php $this->load->view("admin/view_header",array('url'=>'admin_plotes/index/'));
$ci =& get_instance();

?>
<style>
td a
{
    color: green;
}

</style>
            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?>
                    </center>
                </section>
    
    <section class="content-header">
                    <h1 style="background-color: #76DA76;" class="alert btn-success">
                        Add Plote 
                        <small>Control panel</small>
                    </h1>
                </section>
<section class="content">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-6" role="form">
   
   <div class="form-group">
      <div class="col-sm-8">
         <input type="hidden" value="<?=$scheme->id;?>" name="id" />
         <input name="name"class="form-control" value="<?=set_value('name');?>" required="" placeholder="Enter Plote Name" type="text"/>
         <?=form_error('name');?>
      </div>
   </div>
   <div class="form-group">
      <div class="col-sm-10">
          <input name="submit" value="Add"  class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>
<section class="content-header">
                    <table class="table table-bordered">
                    <tr>
                      <th colspan="2" style="text-align:center;font-size: 35px;">Total Area</th>
                      <th colspan="2" style="text-align:center;font-size: 35px;">Saled Area</th>
                      <th colspan="2" style="text-align:center;font-size: 35px;">Remaning Area</th>
                    </tr>
                    <tr>
                      <th style="text-align: center;">Marla</th>
                      <th style="text-align: center;">Feets</th>
                      <th style="text-align: center;">Marla</th>
                      <th style="text-align: center;">Feets</th>
                      <th style="text-align: center;">Marla</th>
                      <th style="text-align: center;">Feets</th>
                    </tr>
                    <tr>
                    <?php
                    $sTotalMarlay=(((float)$scheme->marlay*225)+(float)$scheme->feets)/225;
                    $sf=explode('.',$sTotalMarlay);
                    
                    ?>
                      <td style="text-align: center;"><?=$sf[0];?></td>
                      <td style="text-align: center;"><?php if(isset($sf[1])){echo ceil(('0.'.$sf[1])*225);}else{echo 0;}?></td>
                     
                      <?php $f=explode('.',$totalMrlays);?>
                      <td style="text-align: center;"><?=$f[0];?></td>
                      <td style="text-align: center;"><?php if(isset($f[1])){echo ceil(('0.'.$f[1])*225);}else{echo 0;}?></td>
                    
                      <?php $rf=explode('.',($sTotalMarlay-$totalMrlays));?>
                      <td style="text-align: center;"><?=$rf[0];?></td>
                      <td style="text-align: center;"><?php if(isset($rf[1])){echo ceil(('0.'.$rf[1])*225);}else{echo 0;}?></td>
                    </tr>
                    </table>
</section>
<script>
$(document).ready(function(){
  
  $("#deletee").click(function(){
    $('.delete').toggle();
  });
  
  $("#edite").click(function(){
    $('.edit').toggle();
  });
  
  $("#instalmente").click(function(){
    $('.instalment').toggle();
  });
  
});

</script>
<style>
.delete,.edit,.instalment
{
    display: none;
}
</style>    
    <section class="content-header">
                    <h1>
                        All Plote 
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="<?=base_url('admin_more_detail/index/'.$scheme->id);?>" class="btn btn-warning"><i class=""></i> More Detail</a></li>
                        <li><a class="btn btn-danger" id="deletee"><i class=""></i> Delete</a></li>
                        <li><a class="btn btn-success" id="edite"><i class=""></i> Edit</a></li>
                    </ol>
                </section>
    <section class="content-header">
        <table class="table table-bordered">
                        <tbody><tr>
                            <th>Plote No:</th>
                            <th>User Name</th>
                            <th>Marlay</th>
                            <th>SFT</th>
                            <th>Total Amount</th>
                            <th>Receved Amount</th>
                            <th>Balance</th>
                            <th>Short Payment</th>
                            <th>Commision</th>
                            <th class="instalment" style="width: 1px">Instalment</th>
                            <th class="delete" style="width: 1px">Delete</th>
                            <th class="edit" style="width: 1px">Edit</th>
                        </tr>
                   <?php 
                   
                            $TotalAmount=0;
                            $RecevedAmount=0;
                            $Balance=0;
                            $tShortPayment=0;
                            $Commision=0;
                            $marlay=0;
                            $square_feets=0;
                   
                   
                   foreach($query->result() as $row){
                    
                            $this->db->where(array('plote_id'=>$row->pid));
                            
                            //$this->db->where(array('plote_id'=>$row->pid,'user_cnic'=>$row->cnic));
                            
                            $this->db->select_sum('received_instalment');
                            $queryy = $this->db->get('instalment');
                            
                            foreach($queryy->result() as $r){}
                           
                            $shortPayment=0;
                            if(!empty($row->total_amount))
                            $shortPayment=$ci->shortPayment($row);
                            $marlay += $row->marlay;
                            $square_feets += $row->square_feets;
                            
                            
                            
                            $this->db->where(array('plot_id'=>$row->pid,'scheme_id'=>$scheme->id));
                            $this->db->from('registry');
                            $isRegistry=$this->db->count_all_results();
                            
                    ?>     
                        <tr>
                            <td>
                              <a style="width: 100%;height: 100%;" href="<?php if(empty($row->total_amount)){echo base_url('admin_users/index/'.$row->pid.'/'.$scheme->id);}else{echo '#';}?>"><?=$row->pname;?></a>
                            
                              <?=($isRegistry>0)?'<span style="float: right;" class="btn btn-success">R</span>':'';?>
                              <?=($row->file==1)?'<span style="float: right;" class="btn btn-success">F</span>':'';?>
                              <?=($row->q_slip==1)?'<span style="float: right;" class="btn btn-success">Q</span>':'';?>
                              
                            </td>
                            <td><?php
                            if(!empty($row->total_amount))
                            {
                                echo '<a style="width: 100%;height: 100%;" href="'.base_url('admin_detail/index/'.$row->pid.'/'.$scheme->id).'">'.$row->name.'</a>';
                            }
                            else
                            {
                                echo $row->name;    
                            }
                            ?></td>
                            <td><?=$row->marlay;?></td>
                            <td><?=$row->square_feets;?></td>
                            <td><?=get_price(round($row->total_amount)); $TotalAmount +=round($row->total_amount);?></td>
                            <td><?=get_price(round($r->received_instalment+$row->first_amount)); $RecevedAmount +=round($r->received_instalment+$row->first_amount);?></td>
                            <td><?=get_price(round($row->total_amount-($r->received_instalment+$row->first_amount))); $Balance +=round($row->total_amount-($r->received_instalment+$row->first_amount));?></td>
                            <td><?=get_price(round($shortPayment)); $tShortPayment +=round($shortPayment);?></td>
                            <td><?=get_price(round($row->dealer_commision)); $Commision +=round($row->dealer_commision);?></td>
                            <td class="instalment">
                                <a href="<?php if(!empty($row->total_amount)){echo base_url('admin_instalment/index/'.$row->pid);}else{echo '#';}?>" class="btn btn-warning">Add Instalment</a>
                            </td>
                            <td class="delete">    
                                <a onclick="return confirm('Are you sure you want to delete <?=$row->pname; ?> number plote.');" href="<?=base_url('admin_plotes/delete/'.$row->pid.'/'.$scheme->id);?>" class="btn btn-danger">DELETE</a>
                            </td>
                            <td class="edit">    
                                <a href="<?=base_url('admin_plotes/edit/'.$row->pid.'/'.$scheme->id);?>" class="btn btn-success">Edit</a>
                            </td>
                        </tr>
                  <?php }?>
                           <tr style="background-color: #BCC5EB;">
                               <td>Grand Total</td>
                               <td></td>
                               <td><?=$marlay;?></td>
                               <td><?=$square_feets;?></td>
                               <td><?=get_price($TotalAmount);?></td>
                               <td><?=get_price($RecevedAmount);?></td>
                               <td><?=get_price($Balance);?></td>
                               <td><?=get_price($tShortPayment);?></td>
                               <td><?=get_price($Commision);?></td>
                               <td class="instalment"></td>
                               <td class="delete"></td>
                               <td class="edit"></td>
                            
                            </tr>
                    </tbody></table>
                </section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>