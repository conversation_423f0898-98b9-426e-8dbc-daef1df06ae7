﻿<?php $this->load->view("admin/view_header",array('url'=>'admin_plotes/index/'));
$ci =& get_instance();
?>
<style>
td a
{
    color: green;
}

</style>
            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 110px;
font-family: monospace;">
                        <img  src="<?=base_url('logo.png');?>" style="width: 100%;height: 250px;" /> 
                    </center>
                </section>
    
    <section class="content-header">    
                    <h1 id="nnewadd" style="background-color: #76DA76;top: 15px; cursor: pointer;" class="alert btn-success">
                        Add New Scheme
                        <small>Control panel</small>
                    </h1>
                    
                </section>


<section id="newadd" style="display: none;" class="content">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal" role="form">
   
   <div class="form-group">
      <div class="col-sm-2">
         <input name="name" class="form-control" value="" required="" placeholder="Enter Scheme Name" type="text"/>      
      </div>
       <div class="col-sm-2">
         <input name="marlay" class="form-control" value="" placeholder="Enter Marlay" step="any" type="number"/>    
      </div>
       <div class="col-sm-2">   
         <input name="feets" class="form-control" value="" placeholder="Enter Feets" step="any" type="number"/>      
      </div>
      <div class="col-sm-2">   
         <input name="amount" class="form-control" value="" placeholder="Enter Total Amount" step="any" type="number"/>      
      </div>
      <div class="col-sm-2">   
         <input name="first_amount" class="form-control" value="" placeholder="Enter First Amount" step="any" type="number"/>      
      </div>
   </div>
   <div class="form-group">
      <div class="col-sm-10">
          <input name="submit" value="Add"  class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>
<script>
$(document).ready(function(){
  
  $("#deletee").click(function(){
    $('.delete').toggle();
  });
  
  $("#edite").click(function(){
    $('.edit').toggle();
  });
  
  $("#developmente").click(function(){
    $('.development').toggle();
  });
  
  $("#blackliste").click(function(){
    $('.blacklist').toggle();
  });
  
  $("#cancele").click(function(){
    $('.cancel').toggle();
  });
  
  $("#nnewadd").click(function(){
    $('#newadd').toggle();
  });    
  
  $("#invoicee").click(function(){
    $('.invoiceee').toggle();
  });
  
});

</script>
<style>
.delete,.edit,.development,.blacklist,.cancel,.invoiceee
{
    display: none;
}
</style>

    <section class="content-header">
                    <h1>
                        All Scheme 
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a class="btn btn-success" id="invoicee"><i class=""></i> Invoice</a></li>
                        <li><a class="btn btn-danger" id="deletee"><i class=""></i> Delete</a></li>
                        <li><a class="btn btn-success" id="edite"><i class=""></i> Edit</a></li>
                        <li><a class="btn btn-warning" id="developmente"><i class=""></i> Development</a></li>
                        <li><a class="btn btn-primary" id="blackliste"><i class=""></i> Black List</a></li>
                        <li><a class="btn btn-danger" id="cancele"><i class=""></i> Cancel List</a></li>
                    </ol>
                </section>
    <section class="content-header">
        <table class="table table-bordered">
                        <tbody><tr>
                            <th>Name</th>
                            <th>Total Amount</th>
                            <th>Total Received Amount</th>
                            <th>Remaning Balance</th>
                            <th>Total Short Payment</th>
                            <th>Commision</th>
                            <th class="invoiceee" style="width: 1px;">Invoice</th>
                            <th class="delete" style="width: 1px;">Delete</th>
                            <th class="edit" style="width: 1px;">Edit</th>
                            <th class="development" style="width: 1px;">Development</th>
                            <th class="blacklist" style="width: 1px;">Black List</th>
                            <th class="cancel" style="width: 1px;">Cancel List</th
                        </tr>
                   <?php
                        $t_total_amount=0;
                        $t_total_received_amount=0;
                        $t_Remaning=0;
                        $t_shortPayment=0;
                        $t_commision=0;
                   
                    foreach($query->result() as $row){
                    
                        $total_amount=0;
                        $total_received_amount=0;
                        $shortPayment=0;
                        $commision=0;
                        
                        $p=$this->db->get_where('plots',array('scheme_id'=>$row->id));
                        
                        foreach($p->result() as $plote)
                        {
                            $u=$this->db->get_where('users',array('plot_id'=>$plote->id));
                            
                            $i=$this->db->get_where('instalment',array('plote_id'=>$plote->id));
                            
                            foreach($u->result() as $user)
                            {
                               $total_amount += $user->total_amount;
                               $total_received_amount += $user->first_amount; 
                               if(!empty($user->total_amount))
                               $shortPayment += $ci->shortPayment($user);
                               $commision += $user->dealer_commision;
                            }
                           
                            foreach($i->result() as $instalment)
                            {
                                $total_received_amount += $instalment->received_instalment;
                            }
                        }
                    
                    
                    ?>     
                        <tr>
                            <td><a style="width: 100%;height: 100%;" href="<?=base_url('admin_plotes/index/'.$row->id)?>"><?=$row->name;?></a></td>
                            <td><?php echo get_price(round($total_amount)); $t_total_amount += $total_amount;?></td>
                            <td><?php echo get_price(round($total_received_amount)); $t_total_received_amount += $total_received_amount;?></td>
                            <td><?php echo get_price(round($total_amount-$total_received_amount)); $t_Remaning += ($total_amount-$total_received_amount);?></td>
                            <td><?php echo get_price(round($shortPayment)); $t_shortPayment += round($shortPayment);?></td>
                            <td><?php echo get_price($commision); $t_commision += $commision;?></td>
                            <td class="invoiceee">    
                                <a href="<?=base_url('admin_scheme_invoice/index/'.$row->id);?>" class="btn btn-success">Invoice</a>
                            </td>
                            <td class="delete">
                                <a onclick="return confirm('Are you sure you want to delete <?=$row->name; ?> Scheme.');" href="<?=base_url('admin/delete/'.$row->id);?>" class="btn btn-danger">DELETE</a>
                            </td>
                            <td class="edit">  
                                <a href="<?=base_url('admin/edit/'.$row->id);?>" class="btn btn-success">Edit</a>
                            </td>
                            <td class="development">    
                                <a href="<?=base_url('admin_development/index/'.$row->id);?>" class="btn btn-warning">Development</a>
                            </td>
                            <td class="blacklist">    
                                <a href="<?=base_url('admin_black_list/index/'.$row->id);?>" class="btn btn-primary">Black List</a>
                            </td>
                            <td class="cancel">    
                                <a href="<?=base_url('admin_cancel_list/index/'.$row->id);?>" class="btn btn-danger">Cancel List</a>
                            </td>
                        </tr>
                  <?php }?>
                        <tr style="background-color: #BCC5EB;">
                           <td>Grand Total</td>
                           <td><?=get_price(round($t_total_amount));?></td>
                           <td><?=get_price(round($t_total_received_amount));?></td>
                           <td><?=get_price(round($t_Remaning));?></td>
                           <td><?=get_price(round($t_shortPayment));?></td>
                           <td><?=get_price(round($t_commision));?></td>
                           
                           <td class="invoiceee"></td>
                           <td class="delete"></td>
                           <td class="edit"></td>
                           <td class="development"></td>
                           <td class="blacklist"></td>
                        </tr>
                    </tbody></table>
                </section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>