<?php $this->load->view("admin/view_header",array('url'=>'admin_development/index/'));?>
<script>
function cheque(str)
{
    if(str=='Cash')
    {
      $('#cheque').hide();  
    }
    else
    {
        $('#cheque').show();
    }
}

</script>
<aside class="right-side">                
                <!-- Content Header (Page header) -->
                <section class="content-header">
                    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?><br />
                        Development
                    </center>
                </section>
                </section>
<section class="content" id="sliderrrrr">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-12" role="form">
      <div class="form-group col-sm-2">
      <div class="col-sm-11">
<script>
$(function() {
$( "#datepicker" ).datepicker();
});
</script>
         <label>Date:</label>
         <input name="date"  id="datepicker" value="<?php
            $sdate=explode('-',$development->date);
            echo $sdate[1].'/'.$sdate[2].'/'.$sdate[0]; 
            ?>" class="form-control" value=""  placeholder="MM/DD/YYYY" type="text"/>
      </div>
   </div>
   <div class="form-group col-sm-4">
      <div class="col-sm-11">
      <label>Remarks:</label>
         <textarea name="remarks"  style="width: 260px; height: 36px;" placeholder="Remarks" class="form-control"><?=$development->remarks;?></textarea>
         <?=form_error('remarks');?>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
         <label>Amount:</label>
         <input name="amount"  class="form-control" value="<?=$development->price;?>"  placeholder="Enter received_amount" type="number" step="any"/>
         <?=form_error('amount');?>
      </div>
   </div>
   <div class="form-group col-sm-2">
      <div class="col-sm-11">
             <label>Amount Type:</label>
             <select name="amount_status" onchange="cheque(this.value);" class="form-control" >
                  <option <?php if($development->amount_type=='Cash'){echo 'selected=""';}?>>Cash</option>
                  <option <?php if($development->amount_type=='Cheque'){echo 'selected=""';}?>>Cheque</option>
                  <option <?php if($development->amount_type=='Cash+Cheque'){echo 'selected=""';}?>>Cash+Cheque</option>
              </select>
              <?=form_error('amount_status');?>
      </div>
   </div>
   <?php
   $ch=form_error('cheque_number');
   
   ?>
   <div id="cheque" <?php if(empty($ch) && empty($development->cheque_number)){echo 'style="display:none"';}?> class="form-group col-sm-2">
      <div class="col-sm-11">
         <label>Cheque No:</label>
         <input name="cheque_number"  class="form-control" value="<?=$development->cheque_number;?>"  placeholder="Enter received_amount" type="text"/>
         <?=form_error('cheque_number');?>
      </div>
   </div>
   <div class="form-group col-sm-12">
      <div class="col-sm-11">
          <input name="submit" value="Submit"  class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>
            </aside><!-- /.right-side -->


<?php $this->load->view("admin/view_footer");?>