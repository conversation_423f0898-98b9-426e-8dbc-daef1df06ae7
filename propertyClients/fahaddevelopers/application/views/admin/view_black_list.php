<?php $this->load->view("admin/view_header",array('url'=>'admin_black_list/index/'));
$ci =& get_instance();

?>

            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?>
                    </center>
                </section>
    
    <section class="content-header">
                    <h1 class="alert btn-primary">
                        Black List Plote 
                        <small>Control panel</small>
                    </h1>
                    
                </section>
    <section class="content-header">
        <table class="table table-bordered">
                        <tbody><tr>
                            <th>Plote Number</th>
                            <th>User Name</th>
                            <th>Mobile</th>
                            <th>Marlay</th>
                            <th>SFT</th>
                            <th>Total Amount</th>
                            <th>Receved Amount</th>
                            <th>Balance</th>
                            <th>Short Payment</th>
                            <th>Commision</th>
                            <th style="width: 1px">Action</th>
                        </tr>
                   <?php foreach($query->result() as $row){
                    
                            $this->db->where('plote_id',$row->pid);
                            $this->db->select_sum('received_instalment');
                            $query = $this->db->get('instalment');
                           
                            $shortPayment=0;
                            if(!empty($row->total_amount))
                            $shortPayment=$ci->shortPayment($row->pid,$row->sdate,$row->edate,$row->total_amount,$row->first_amount,$row->instalment_months);
                            
                            $instalment_per_month=($row->instalment_months==0) ? 0 : (round(($row->total_amount-$row->first_amount)/$row->instalment_months));
                        
                            
                        
                        
                            
                      if((($instalment_per_month*3) <= $shortPayment && $shortPayment > 0) || ($row->edate<date('Y-m-d') && $shortPayment > 0))
                      {      
                            
                            foreach($query->result() as $r){}
                    ?>     
                        <tr>
                            <td><a style="width: 100%;height: 100%;" href="<?php if(empty($row->total_amount)){echo base_url('admin_users/index/'.$row->pid.'/'.$scheme->id);}else{echo '#';}?>"><?=$row->pname;?></a></td>
                            <td><?php
                            if(!empty($row->total_amount))
                            {
                                echo '<a style="width: 100%;height: 100%;" href="'.base_url('admin_detail/index/'.$row->pid.'/'.$scheme->id).'">'.$row->name.'</a>';
                            }
                            else
                            {
                                echo $row->name;    
                            }
                            
                            ?></td>
                            <td><?=$row->mobile;?></td>
                            <td><?=$row->marlay;?></td>
                            <td><?=$row->square_feets;?></td>
                            <td><?=get_price(round($row->total_amount))?></td>
                            <td><?=get_price(round($r->received_instalment+$row->first_amount));?></td>
                            <td><?=get_price(round($row->total_amount-($r->received_instalment+$row->first_amount)));?></td>
                            <td><?=get_price(round($shortPayment));?></td>
                            <td><?=get_price(round($row->dealer_commision));?></td>
                            <td>
                                <a href="<?php if(!empty($row->total_amount)){echo base_url('admin_instalment/index/'.$row->pid);}else{echo '#';}?>" class="btn btn-success">Add Instalment</a>
                            </td>
                        </tr>
                  <?php }}?>
                    </tbody></table>
                </section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>