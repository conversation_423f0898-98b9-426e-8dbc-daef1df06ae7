<?php $this->load->view("admin/view_header_plotes");?>
<script>
function cheque(str)
{
    if(str=='Cash')
    {
      $('#cheque').hide();  
      $('#cheque_number').val('');
    }
    else
    {
        $('#cheque').show();
    }
}
function active()
{
    $('#error').hide();
    $('#submit').removeAttr("disabled");
    
}
</script>
            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
    <section class="content-header">
                    <center style="font-size: 80px;font-family: monospace;">
                        <?=$scheme->name;?><br />
                        <h2><?=$plote->name;?></h2>
                    </center>
                </section> 
<script>
$(document).ready(function(){
  $("#testttt").click(function(){
    $('#sliderrrrr').slideToggle();
  });
});
</script> 
    <section class="content-header">
                    <h1 style="cursor: pointer;" id="testttt">
                        Instalment
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section class="content" id="sliderrrrr">
<?=$error;?>
<form method="post" action="">
<div class="form-horizontal col-sm-12" role="form">
   <input type="hidden" name="user_id" value="<?=$user->id;?>" />
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
      <label>Slip Number:</label>
         <input name="slip_number" required="" onkeyup="document.getElementById('error').remove();" class="form-control" value="<?=set_value('slip_number');?>" required="" placeholder="Enter slip_number" type="text"/>
      </div>
   </div>
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
         <label>Received Instalment:</label>
         <input name="received_amount" required="" class="form-control" value="<?=set_value('received_amount');?>" required="" placeholder="Enter received_amount" type="text"/>
      </div>
   </div>
   <div class="form-group col-sm-2">
      <div class="col-sm-11">
             <label>Amount Status:</label>
             <select name="amount_status" onchange="cheque(this.value);" class="form-control" >
                  <option <?=set_select('amount_status', 'Cash'); ?>>Cash</option>
                  <option <?=set_select('amount_status', 'Cheque'); ?>>Cheque</option>
                  <option <?=set_select('amount_status', 'Cash+Cheque'); ?>>Cash+Cheque</option>
              </select>
      </div>
   </div>
   <?php
   $ch=set_value('cheque_number');
   
   ?>
   <div id="cheque" <?php if(empty($ch)){echo 'style="display:none"';}?> class="form-group col-sm-2">
      <div class="col-sm-11">
         <label>Cheque No:</label>
         <input id="cheque_number" name="cheque_number"  class="form-control" value="<?=set_value('cheque_number');?>"  placeholder="Enter received_amount" type="text"/>
         <?=form_error('cheque_number');?>
      </div>
   </div>
   <div class="form-group col-sm-2">
      <div class="col-sm-11">
<script>
$(function() {
$( "#datepicker" ).datepicker();
});
</script>
         <label>Date:</label>
         <input name="date" required="" id="datepicker" value="<?=set_value('date');?>" class="form-control" value="" required="" placeholder="MM/DD/YYYY" type="text"/>
      </div>
   </div><br /><br /><br /><br />
   <div class="form-group col-sm-3">
      <div class="col-sm-11">
          <input name="submit" value="Submit"  class="btn btn-default" type="submit"/>    
      </div>
   </div>
</div>
</form>
</section>
    <section class="content-header">
                    <h1>
                        User Information
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section class="content">
<div class="container col-sm-12">

        <div class="form-group col-sm-4">
          <label>Name:</label>
            <input type="text" value="<?=$user->name;?>" readonly="" name="name" class="form-control" />
        </div>
          <div class="form-group col-sm-4">
          <label>Father Name:</label>
            <input type="text" readonly="" value="<?=$user->fname;?>" name="fname" class="form-control" />
        </div>
          <div class="form-group col-sm-4">
          <label>CNIC:</label>
            <input type="text" readonly="" value="<?=$user->cnic;?>" name="cnic" class="form-control" />
        </div>
          <div class="form-group  col-sm-12">
          <label>Address:</label>
            <input type="text" readonly="" value="<?=$user->address;?>" name="address" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>File Number:</label>
            <input type="text" readonly="" value="<?=$user->file_number;?>" name="file_number" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Mobile:</label>
            <input type="text" readonly="" value="<?=$user->mobile;?>" name="mobile" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Totle Marlay:</label>
            <input type="text" readonly="" value="<?=$user->marlay;?>" name="marlay" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Square Feets:</label>
            <input type="text" readonly="" value="<?=$user->square_feets;?>" name="feets" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Rate Per Marla:</label>
            <input type="text" value="<?=get_price($user->rate_per_marla);?>" readonly="" name="rate_per_marla" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>First Amount:</label>
            <input type="text" readonly="" value="<?=get_price($user->first_amount);?>" name="first_amount" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Amount Status:</label>
              <input type="text" readonly="" value="<?=$user->amount_status;?>" name="first_amount" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Instalment Months:</label>
            <input type="text" readonly="" value="<?=$user->instalment_months;?>" name="instalment_months" class="form-control" />
        </div>
        
        
          <div class="form-group col-sm-3">
          <label>Total Amount:</label>
            <input type="text" value="<?=get_price($user->total_amount);?>" readonly="" name="total_amount" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Total Received Amount:</label>
            <input type="text" readonly="" value="<?=get_price($total_received+$user->first_amount);?>" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Remaining Balance:</label>
            <input type="text" readonly="" value="<?=get_price($user->total_amount-($total_received+$user->first_amount));?>" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Per Month Instalment:</label>
            <input type="text" readonly="" value="<?=($user->instalment_months==0)?($user->total_amount-$user->first_amount):get_price(round(($user->total_amount-$user->first_amount)/$user->instalment_months));;?>" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Start Date:</label>
            <input type="text" name="sdate" readonly="" value="<?=$user->sdate;?>" id="datepicker" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>End Date:</label>
            <input type="text" value="<?=$user->edate;?>" readonly="" name="edate" id="datepicker1" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Months Now A Day:</label>
            <input type="text" value="<?=$shortMonth;?>" readonly="" class="form-control" />
        </div>
        <div class="form-group col-sm-3">
          <label>Short Payment:</label>
            <input type="text" value="<?=get_price(round($shortPayment));?>" readonly="" class="form-control" />
        </div>
    </div>

</section>
    <section class="content-header">
                    <h1>
                        Instalment Detail
                        <small>Control panel</small>
                    </h1>
                    <ol class="breadcrumb">
                        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
                        <li class="active">Blank page</li>
                    </ol>
                </section>
<section>
<table class="table table-bordered">
<tr>
  <th>Slip Number</th>
  <th>Received Instalmen</th>
  <th>Amount Status</th>
  <th>Cheque Number</th>
  <th>Date</th>
  <th>Action</th>
</tr>
<?php foreach($instalment as $row){?>
<tr>
  <td><?=$row->slip_number;?></td>
  <td><?=$row->received_instalment;?></td>
  <td><?=$row->amount_status;?></td>
  <td><?=$row->cheque_number;?></td>
  <td><?=$row->date;?></td>
  <td><a class="btn btn-danger" onclick="return confirm('press ok for delete this.');" href="<?=base_url('admin_instalment/delete/'.$row->id.'/'.$plote->id);?>">DELETE</a>
  <a class="btn btn-success"  href="<?=base_url('admin_instalment/edit/'.$row->id.'/'.$plote->id);?>">Edit</a></td>
</tr>
<?php }?>
</table>

</section>
            </aside>
        <?php $this->load->view("admin/view_footer");?>