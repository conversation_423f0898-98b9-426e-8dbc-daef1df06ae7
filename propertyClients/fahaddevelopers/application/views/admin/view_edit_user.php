<?php $this->load->view("admin/view_header_plotes",array('scheme'=>$schemes));?>
<script>
function readURL(input) {

    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            //$('#blah').attr('src', e.target.result);
            
            document.getElementById('blah').src=e.target.result;
            
        }

        reader.readAsDataURL(input.files[0]);
    }
}

</script>



            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
                
<section class="content-header">
<img id="blah" style="margin-bottom: -264px;width: 150px;" alt="" src="<?php
if(file_exists(base_url('uploads/'.$user->image)))
{
    echo base_url('uploads/'.$user->image);
}
?>" /> 
                    <center  style="font-size: 80px;font-family: monospace;">
                        <?=$schemes->name;?><br />
                        <?=$plots->name;?> 
                    </center>
                </section>
    
    <section class="content-header">
                    <h1 style="background-color: #76DA76;" class="alert btn-success">
                       Edit User 
                        
                    </h1>
                    
                </section>
<script>

function gettotal()
{
   var marlay = document.getElementById('marlay').value;
   var rate_per_marla = document.getElementById('rate_per_marla').value;
   var feets = document.getElementById('feets').value;
      
   document.getElementById('total_amount').value=Math.round(((rate_per_marla/225)*feets)+(marlay*rate_per_marla));   
}
function getinstalment()
{
    //per_month_instalment
    var first_amount = document.getElementById('first_amount').value;
    var instalment_months = document.getElementById('instalment_months').value;
    var total_amount = document.getElementById('total_amount').value;

    document.getElementById('per_month_instalment').value=Math.round((total_amount-first_amount)/instalment_months);
}

function getdate()
{
    //alert('sdf');
    var sdate = document.getElementById('sdate').value;
    sdate = sdate.split("/");
    
    var edate = document.getElementById('edate').value;
    edate = edate.split("/");
    
    
    var a = (((edate[2]-sdate[2])-1)*12);
    var b = parseInt(edate[0])+parseInt(12-sdate[0]);
    //instalment_months
    document.getElementById('instalment_months').value=Math.round((parseInt(a)+parseInt(b)));
}

function check()
{
    gettotal();
    getinstalment();
    getdate();
}

</script>

<section class="content">
<div class="container col-sm-12">
<?=$error;?>
      <form method="post" action="" enctype="multipart/form-data" >
        <div class="form-group col-sm-4">
          <label>Name:</label>
            <input type="text" value="<?=$user->name;?>" name="name" class="form-control" />

        </div>
          <div class="form-group col-sm-4">
          <label>Father Name:</label>
            <input type="text" value="<?=$user->fname;?>" name="fname" class="form-control" />

        </div>
          <div class="form-group col-sm-4">
          <label>CNIC:</label>
            <input type="text" value="<?=$user->cnic;?>" name="cnic" class="form-control" />

        </div>
          <div class="form-group  col-sm-12">
          <label>Address:</label>
            <input type="text" value="<?=$user->address;?>" name="address" class="form-control" />

        </div>
          <div class="form-group col-sm-3">
          <label>File Number:</label>
            <input type="text" value="<?=$user->file_number;?>" name="file_number" class="form-control" />

        </div>
        <div class="form-group col-sm-3">
          <label>Mobile:</label>
            <input type="text" value="<?=$user->mobile;?>" name="mobile" class="form-control" />

        </div>
          <div class="form-group col-sm-3">
          <label>Totle Marlay:</label>
            <input type="number" step="any" value="<?=$user->marlay;?>" id="marlay" onkeyup="check();" name="marlay" class="form-control" />

        </div>
          <div class="form-group col-sm-3">
          <label>Square Feets:</label>
            <input type="number" step="any" value="<?=$user->square_feets;?>" id="feets" onkeyup="check();" name="feets" class="form-control" />

        </div>
          <div class="form-group col-sm-3">
          <label>Rate Per Marla:</label>
            <input type="number" step="any" value="<?=$user->rate_per_marla;?>" id="rate_per_marla" onkeyup="check();" name="rate_per_marla" class="form-control" />

        </div>
          <div class="form-group col-sm-3">
          <label>First Amount:</label>
            <input type="number" step="any" value="<?=$user->first_amount;?>" id="first_amount" onkeyup="check();" name="first_amount" class="form-control" />

        </div>
          <div class="form-group col-sm-3">
          <label>Amount Status:</label>
              <select name="amount_status" class="form-control" >
                  <option>Cash</option>
                  <option>Cheque</option>
                  <option>Cash+Cheque</option>
              </select>
        </div>
        <div class="form-group col-sm-3">
          <label>Per Month Instalment:</label>
            <input value="<?=($user->instalment_months==0) ? 0 : get_price(round(($user->total_amount-$user->first_amount)/$user->instalment_months));?>" type="text" id="per_month_instalment" readonly="" class="form-control" />
        </div>
          <div class="form-group col-sm-3">
          <label>Dealer Name:</label>
            <input value="<?=$user->dealer;?>" type="text" name="dealer" class="form-control" />
            
        </div>
          <div class="form-group col-sm-3">
          <label>Dealer Commision:</label>
            <input type="number" step="any" value="<?=$user->dealer_commision;?>" name="dealer_commision" class="form-control" />
            
        </div>
          <div class="form-group col-sm-3">
          <label>Total Amount:</label>
            <input type="text" value="<?=$user->total_amount;?>" id="total_amount" readonly="" name="total_amount" class="form-control" />
            
        </div>
          <div class="form-group col-sm-3">
<script>
$(function() {
$( "#sdate" ).datepicker();
});
</script>
          <label>Start Date:</label>
            <input type="text" name="sdate" id="sdate" onchange="check();" value="<?php
            $sdate=explode('-',$user->sdate);
            echo $sdate[1].'/'.$sdate[2].'/'.$sdate[0]; 
            ?>"  class="form-control" />
            
        </div>
        <div class="form-group col-sm-3">
<script>
$(function() {
$( "#edate" ).datepicker();
});
</script>
          <label>End Date:</label>
            <input type="text" value="<?php
            $edate=explode('-',$user->edate);
            echo $edate[1].'/'.$edate[2].'/'.$edate[0]; 
            ?>" id="edate" onchange="check();" name="edate" class="form-control" />
           
        </div>
          <div class="form-group col-sm-3">
          <label>Instalment Months:</label>
            <input type="text" value="<?=$user->instalment_months;?>" readonly="" onchange="check();" id="instalment_months" name="instalment_months" class="form-control" />
            
        </div>
        
        <div class="form-group col-sm-2">
          <label>User image:</label>
            <input type="file" onchange="readURL(this);" name="img" class="btn btn-success" class="form-control" />
        </div>
        <div class="form-group col-sm-6">
          <input type="submit" name="Submit" class="btn btn-default" value="Submit" />
        </div>
      </form>
    </div>

</section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>