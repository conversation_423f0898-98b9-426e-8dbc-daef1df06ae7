<?php $this->load->view("admin/view_search_header",array('url'=>'admin_plotes/index/'));?>

            <!-- Right side column. Contains the navbar and content of the page -->
            <aside class="right-side">                
    
    <section class="content-header">    
                    <h1 id="nnewadd" style="background-color: #76DA76;top: 15px; cursor: pointer;" class="alert btn-success">
                        Add New Registry
                        <small>Control panel</small>
                    </h1>
                    
                </section>


<section id="newadd" style="display: none;" class="content">
<center>
<a href="<?=base_url('admin_registry/?c=true');?>" class="btn btn-success" style="height: 100px;font-size: 45px;">Company</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="<?=base_url('admin_registry');?>" class="btn btn-success" style="height: 100px;font-size: 45px;">Non Company</a>
</center>
</section>
<script>
$(document).ready(function(){
  
  
  $("#nnewadd").click(function(){
    $('#newadd').toggle();
  }); 
  
});

</script>

<section class="content">
    <div class="container col-sm-12">
<table class="table table-bordered">
<tr>
   <th>Sr_No:</th>
   <th>Date:</th>
   <th>Saler:</th>
   <th>Saler Father</th>
   <th>Pachaser:</th>
   <th>Pachaser Father</th>
   <th>Scheme:</th>
   <th>Area:</th>
   <th>cost:</th>
   <th>Received:</th>
   <th>Remaining:</th>
   <th>Action:</th>
   
</tr>
<?php 
$tcost=0;
$tReceived=0;
$tRemaining=0;


foreach($data as $row){

$this->db->select("sum(amount) as sum");
$this->db->from('registry_received');
$this->db->where('registry_ID',$row->id);
$r=$this->db->get()->row();    
    
?>

<tr>
   <td><?=$row->sr_no;?></td>
   <td><?=$row->date;?></td>
   <td><?=$row->saler;?></td> 
   <td><?=$row->saler_father_name;?></td> 
   <td><?=$row->pachaser;?></td>
   <td><?=$row->pachaser_father_name;?></td>
   <td><?=$row->scheme;?></td>
   <td><?=$row->marlay;?></td>
   <td><?=$row->cost; $tcost +=$row->cost;?></td>
   <td><?=$r->sum; $tReceived +=$r->sum;?></td>
   <td><?=($row->cost-$r->sum); $tRemaining += ($row->cost-$r->sum);?></td>
   <td>
   <a href="<?=base_url('admin_all_registry/edit/'.$row->id);?>" class="btn btn-success">Edit</a>
   <a href="<?=base_url('admin_all_registry/delete/'.$row->id);?>" onclick="confirm('press ok for delete this.');" class="btn btn-danger">Delete</a>
   </td>
</tr>

<?php }?>
<tr>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td></td>
   <td><?=$tcost;?></td>
   <td><?=$tReceived;?></td>
   <td><?=$tRemaining;?></td>
   <td></td>
   <td></td>
</tr>
</table>
    </div>

</section>


            </aside>
        <?php $this->load->view("admin/view_footer");?>