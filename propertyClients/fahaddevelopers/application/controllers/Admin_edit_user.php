<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_edit_user extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
    public function index($id,$sid)
	{
	   $error="";
        
        if($this->input->post('Submit'))
        {
            
            $this->form_validation->set_rules('name','Name','required|xss_clean');
            $this->form_validation->set_rules('fname','Father Name','required|xss_clean');
            $this->form_validation->set_rules('cnic','CNIC','required|xss_clean');
            $this->form_validation->set_rules('address','Address','required|xss_clean');
            $this->form_validation->set_rules('file_number','File Number','required|xss_clean');
            $this->form_validation->set_rules('mobile','Mobile','required|xss_clean');
            $this->form_validation->set_rules('marlay','Total Marlay','required|xss_clean');
            $this->form_validation->set_rules('feets','Square Feets','required|xss_clean');
            $this->form_validation->set_rules('rate_per_marla','Rate Per Marla','required|xss_clean');
            $this->form_validation->set_rules('first_amount','First Amount','required|xss_clean');
            $this->form_validation->set_rules('amount_status','Amount Status','required|xss_clean');
            $this->form_validation->set_rules('dealer','Dealer Name','required|xss_clean');
            $this->form_validation->set_rules('dealer_commision','Dealer Commision','required|xss_clean');
            $this->form_validation->set_rules('total_amount','Total Amount','required|xss_clean');
            $this->form_validation->set_rules('sdate','Start Date','required|xss_clean');
            $this->form_validation->set_rules('edate','End Date','required|xss_clean');
            $this->form_validation->set_rules('instalment_months','Instalment Months','required|xss_clean');
            $this->form_validation->set_rules('pid','Plote','|is_unique[users.plot_id]');
            
            $this->form_validation->set_error_delimiters('<div class="alert alert-danger">','</div>');
            
            if($this->form_validation->run() !== false)
            {
                
                $sdate=explode('/',$this->input->post('sdate'));
                $edate=explode('/',$this->input->post('edate'));
                $data=array(
                'name'=>$this->input->post('name'), 
                'fname'=>$this->input->post('fname'), 
                'cnic'=>$this->input->post('cnic'), 
                'address'=>$this->input->post('address'), 
                'mobile'=>$this->input->post('mobile'), 
                'marlay'=>$this->input->post('marlay'), 
                'total_amount'=>$this->input->post('total_amount'), 
                'first_amount'=>$this->input->post('first_amount'), 
                'dealer'=>$this->input->post('dealer'), 
                'dealer_commision'=>$this->input->post('dealer_commision'), 
                'instalment_months'=>$this->input->post('instalment_months'), 
                'square_feets'=>$this->input->post('feets'), 
                'rate_per_marla'=>$this->input->post('rate_per_marla'), 
                'amount_status'=>$this->input->post('amount_status'), 
                'file_number'=>$this->input->post('file_number'), 
                'sdate'=>($sdate[2].'-'.$sdate[0].'-'.$sdate[1]), 
                'edate'=>($edate[2].'-'.$edate[0].'-'.$edate[1])
                );
                
                    $this->load->library('upload');
                    $this->upload->initialize($this->set_upload_options());
          
                    if($this->upload->do_upload('img'))
                    {
                        $file=$this->upload->data();
                        $data['image']=$file['file_name'];
                        
                        $u=$this->db->get_where('users',array('plot_id'=>intval($id)));
                        foreach($u->result() as $row)
                        {
                            if(file_exists('uploads/'.$row->image))
                             unlink('uploads/'.$row->image);
                        }
                    }
                    else
                    {
                        $error='<div class="alert alert-danger">'.$this->upload->display_errors().'</div>';
                    }
                
                
                $this->db->where(array('plot_id'=>$id));
                $this->db->update('users',$data);
                
                //$error='<div class="alert alert-success">Updated!</div>';
                
                redirect(base_url('admin_detail/index/'.$id.'/'.$sid));
            }
        }
       $u=$this->db->get_where('users',array('plot_id'=>$id));
        foreach($u->result() as $user){}
       
        $s=$this->db->get_where('plots',array('id'=>$id));
        foreach($s->result() as $plots){}
        
        $ss=$this->db->get_where('schemes',array('id'=>$plots->scheme_id));
       foreach($ss->result() as $schemes){}
       
        $this->load->view('admin/view_edit_user',array('error'=>$error,'plots'=>$plots,'schemes'=>$schemes,'user'=>$user));
	}
    public function checkzero()
    {
        if($this->input->post('instalment_months')==0)
        {
            $_POST['instalment_months']=1;
        }
        return true;
    }
    private function set_upload_options()
    {   
       //  upload an image options
       $config = array();
       $config['upload_path'] = 'uploads';
       $config['allowed_types'] = 'gif|jpg|png';
       $config['overwrite']     = FALSE;
      
       return $config;
    }
}