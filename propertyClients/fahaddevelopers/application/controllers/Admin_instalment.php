<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_instalment extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
	public function index($id)
	{
	   $error="";
       if($this->input->post('submit'))
       {
            $this->form_validation->set_rules('slip_number','Slip Number','required|xss_clean');
            $this->form_validation->set_rules('received_amount','Received Instalment','required|xss_clean');
            $this->form_validation->set_rules('amount_status','Amount Status','required|xss_clean');
            $this->form_validation->set_rules('date','Date','required|xss_clean');
            $this->form_validation->set_rules('cheque_number','Cheque Number','xss_clean|callback_ischeque');
            
            $this->form_validation->set_error_delimiters('<div class="alert alert-danger">','</div>');
            
            if($this->form_validation->run() !== false)
            {
                $ucnic=$this->db->get_where('users',array('id'=>$this->input->post('user_id')))->result();
                
                $sdate=explode('/',$this->input->post('date'));
                $data=array(
                'user_cnic'=>$ucnic[0]->cnic,
                'plote_id'=>intval($id),
                'user_id'=>$this->input->post('user_id'),
                'slip_number'=>$this->input->post('slip_number'),
                'received_instalment'=>$this->input->post('received_amount'),
                'amount_status'=>$this->input->post('amount_status'),
                'date'=>($sdate[2].'-'.$sdate[0].'-'.$sdate[1]),
                'cheque_number'=>$this->input->post('cheque_number')
                );
                
                $this->db->insert('instalment',$data);
                
                if($this->db->affected_rows()>0)
                {
                    $error='<div id="error" class="alert alert-success">Instalment Submited!</div>';
                }
                else
                {
                    $error='<div class="alert alert-danger">Error. Try Again!</div>';
                }
            }
       }
       
       $total_received=0;
       
       $u=$this->db->get_where('users',array('plot_id'=>intval($id)));
       foreach($u->result() as $user){}
       
       if($u->num_rows()==0)
       {
          redirect(base_url('admin_plotes/index/'.$this->uri->segment(4)));
       }
       
       $p=$this->db->get_where('plots',array('id'=>intval($id)));       
	   foreach($p->result() as $plote){}
       
       $s=$this->db->get_where('schemes',array('id'=>$plote->scheme_id));       
	   foreach($s->result() as $scheme){}
       
       $instalment=array();
       $this->db->order_by('date','asc');
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($id),'user_cnic'=>$user->cnic));
       foreach($i->result() as $row)
       {
          $instalment[]=$row;
          $total_received += $row->received_instalment;
       }     
       
       
       //Testing
      
      $user->rate_per_marla=(!empty($user->rate_per_marla_t)) ? $user->rate_per_marla_t :$user->rate_per_marla;
      $user->first_amount=(float)$user->first_amount+(float)$user->transfor_amount;
      $user->total_amount=((($user->rate_per_marla/225)*$user->square_feets)+($user->marlay*$user->rate_per_marla));
      
      //End Testing
       
       
       $shortPayment = $this->shortPayment($id,$user->sdate,$user->edate,$user->total_amount,$user->first_amount,$user->instalment_months,$user->cnic);
       
       $this->load->view('admin/view_instlment',array(
       'error'=>$error,
       'user'=>$user,
       'plote'=>$plote,
       'scheme'=>$scheme,
       'instalment'=>$instalment,
       'total_received'=>$total_received,
       'shortPayment'=>$shortPayment,
       'shortMonth'=>$this->shortMonth($user->sdate)
       ));
	}
    public function shortMonth($sdate)
    {
        $sdate=explode('-',$sdate);
        
        $a = (((date('Y')-$sdate[0])-1)*12);
        $b = (date('m'))+(12-$sdate[1]);
        
        return ($a+$b);
    }
    public function shortPayment($plote_id,$sdate,$edate,$total_amount,$first_amount,$instalment_months,$cnic)
    {
        
       $total_received=0;
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($plote_id),'user_cnic'=>$cnic));  
       foreach($i->result() as $row)
       {
          $total_received += $row->received_instalment;
       } 
        
       if($instalment_months==0) 
       {
          if($edate < date('Y-m-d'))
          {
            return ($total_amount-($total_received+$first_amount));
          }
          else
          {
            return 0;
          }
       }
        
        
        if($edate > date('Y-m-d'))
        {
            $sdate=explode('-',$sdate);
            $a = (((date('Y')-$sdate[0])-1)*12);
            $b = (date('m'))+(12-$sdate[1]);
        }
        else
        {
            $sdate=explode('-',$sdate);
            $edate=explode('-',$edate);
            $a = ((($edate[0]-$sdate[0])-1)*12);
            $b = ($edate[1])+(12-$sdate[1]);
        }
        
        $shortPayment = ($a+$b)
       *(($total_amount-$first_amount)/$instalment_months)
       +$first_amount-$total_amount
       +($total_amount-($total_received+$first_amount));
       
       return $shortPayment;
    }
    public function delete($id,$pid)
    {
        $this->db->where('id',intval($id));
        $this->db->delete('instalment');
        redirect(base_url('admin_instalment/index/'.$pid));
        
    }
    public function edit($id,$pid)
    {
        $error="";
        if($this->input->post('submit'))
       {
            $this->form_validation->set_rules('slip_number','Slip Number','required|xss_clean');
            $this->form_validation->set_rules('received_amount','Received Instalment','required|xss_clean');
            $this->form_validation->set_rules('amount_status','Amount Status','required|xss_clean');
            $this->form_validation->set_rules('date','Date','required|xss_clean');
            $this->form_validation->set_rules('cheque_number','Cheque Number','xss_clean|callback_ischeque');
            
            if($this->form_validation->run() !== false)
            {
                $sdate=explode('/',$this->input->post('date'));
                $data=array(
                'slip_number'=>$this->input->post('slip_number'),
                'received_instalment'=>$this->input->post('received_amount'),
                'amount_status'=>$this->input->post('amount_status'),
                'date'=>($sdate[2].'-'.$sdate[0].'-'.$sdate[1]),
                'cheque_number'=>$this->input->post('cheque_number')
                );
                
                $this->db->where('id',$id);
                $this->db->update('instalment',$data);
                
                if($this->db->affected_rows()>0)
                {
                    redirect(base_url('admin_instalment/index/'.$pid));
                }
                else
                {
                    $error='<div class="alert alert-danger">Error. Try Again!</div>';
                }
            }
       }
       
       $p=$this->db->get_where('plots',array('id'=>intval($pid)));       
	   foreach($p->result() as $plote){}
       
       $s=$this->db->get_where('schemes',array('id'=>$plote->scheme_id));       
	   foreach($s->result() as $scheme){}
       
       $i=$this->db->get_where('instalment',array('id'=>$id));       
	   foreach($i->result() as $instalment){}
       
       $this->load->view('admin/view_edit_instalment',array(
       'error'=>$error,
       'plote'=>$plote,
       'scheme'=>$scheme,
       'instalment'=>$instalment
       ));
       
    }
    public function ischeque()
    {
        $cheque_number=$this->input->post('cheque_number');
        $amount_status=$this->input->post('amount_status');
        
        if($amount_status=="Cash")
        {
            if(!empty($cheque_number))
            {
                $this->form_validation->set_message('ischeque','%s Must be empty.');
                return false;
            }
            return true;
        }
        else
        {
            if(empty($cheque_number))
            {
                $this->form_validation->set_message('ischeque','%s field is required.');
                return false;
            }
            return true;
        }
    }
 }