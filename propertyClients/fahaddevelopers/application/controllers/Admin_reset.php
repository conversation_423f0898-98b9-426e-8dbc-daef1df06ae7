<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_reset extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('index.php/admin_request'));
        }
    }
    public function index()
	{
	   $error="";
	   if($this->input->post('update'))
       {
          $this->form_validation->set_rules('username','Username','required|xss_clean');
          $this->form_validation->set_rules('password','Password','required|md5|xss_clean');
          $this->form_validation->set_error_delimiters('<div class="alert alert-danger">','<div>');
          
          if($this->form_validation->run()!=false)
          {
             $data=array(
             'ra_username' =>$this->input->post('username'),
             'ra_password' =>$this->input->post('password')
             );
             
             $this->db->where(array('ra_id'=>1));
             $this->db->update('ra_admin',$data);
             $error='<div class="alert alert-success">Successfully Updated!</div>';
          }
       }
       
       $this->load->view('admin/view_reset',array('error'=>$error));
	}
 }