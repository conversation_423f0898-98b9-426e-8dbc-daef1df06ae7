<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_black_list extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
    public function index($id)
	{	 
	   $error="";
       
       $s=$this->db->get_where('schemes',array('id'=>$id));
       foreach($s->result() as $scheme){}
        
        $this->db->select('plots.id as pid,plots.name as pname,plots.*,users.*,CAST(plots.name AS UNSIGNED) as str');
	    $this->db->from('plots');
        $this->db->join('users', 'users.plot_id = plots.id', 'left');
        $this->db->order_by("str",'asc');
        $this->db->order_by("plots.name",'asc');
        $this->db->where(array('plots.scheme_id'=>$id));
        $query=$this->db->get();
        
        
        $this->load->view('admin/view_black_list',array('error'=>$error,'query'=>$query,'scheme'=>$scheme));
	}
    public function delete($id,$pid)
    {
        $this->db->where('id',intval($id));
        $this->db->delete('plots');
        
        
        $u=$this->db->get_where('users',array('plot_id'=>intval($id)));
        foreach($u->result() as $row)
        {
            if(file_exists('uploads/'.$row->image))
            unlink('uploads/'.$row->image);
        }
        
        $this->db->where('plot_id',intval($id));
        $this->db->delete('users');
        
        $this->db->where('plote_id',intval($id));
        $this->db->delete('instalment');
        
        redirect(base_url('admin_plotes/index/'.$pid));
    }
    public function shortPayment($plote_id,$sdate,$edate,$total_amount,$first_amount,$instalment_months)
    {
        
       $total_received=0;
       $i=$this->db->get_where('instalment',array('plote_id'=>intval($plote_id)));  
       foreach($i->result() as $row)
       {
          $total_received += $row->received_instalment;
       } 
        
       if($instalment_months==0) 
       {
          if($edate < date('Y-m-d'))
          {
            return ($total_amount-($total_received+$first_amount));
          }
          else
          {
            return 0;
          }
       }
        
        
        if($edate > date('Y-m-d'))
        {
            $sdate=explode('-',$sdate);
            $a = (((date('Y')-$sdate[0])-1)*12);
            $b = (date('m'))+(12-$sdate[1]);
        }
        else
        {
            $sdate=explode('-',$sdate);
            $edate=explode('-',$edate);
            $a = ((($edate[0]-$sdate[0])-1)*12);
            $b = ($edate[1])+(12-$sdate[1]);
        }
        
        $shortPayment = ($a+$b)
       *(($total_amount-$first_amount)/$instalment_months)
       +$first_amount-$total_amount
       +($total_amount-($total_received+$first_amount));
       
       return $shortPayment;
    }
    public function edit($id,$sid)
    {
        $error="";
       if($this->input->post('submit'))
       {
          $this->form_validation->set_rules('name','Plote Name','required|callback_is_plote[sid]|xss_clean');
          
          $this->form_validation->set_message('is_plote','%s is already in use.');
          if($this->form_validation->run() !== false)
          {
            $this->db->where('id',intval($id));
            $this->db->update('plots',array('name'=>$this->input->post('name')));
            $error='<div class="alert alert-success">Plote Updated!</div>';
            redirect(base_url('admin_plotes/index/'.$sid));
          }
       }
        $g=$this->db->get_where('plots',array('id'=>intval($id)));
        
        foreach($g->result() as $get){}
        
        $this->load->view('admin/view_edit_plote',array('error'=>$error,'get'=>$get));
    }
    public function is_plote($name)
    {
        $id=$this->input->post('id');
        $p=$this->db->get_where('plots',array('name'=>$name,'scheme_id'=>$id));
        
        if($p->num_rows() > 0)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
}