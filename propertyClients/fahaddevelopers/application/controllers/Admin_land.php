<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Admin_land extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
    public function index($id)
	{	
	    $error="";
        
        if($this->input->post('submit'))
        {
            $this->form_validation->set_rules('date','Date','required|xss_clean');
            $this->form_validation->set_rules('remarks','Remarks','required|xss_clean');
            $this->form_validation->set_rules('amount','Amount','required|xss_clean');
            $this->form_validation->set_rules('amount_status','Amount Type','required|xss_clean');
            $this->form_validation->set_rules('cheque_number','Cheque Number','xss_clean|callback_ischeque');
            
            if($this->form_validation->run() !== false)
            {
                $sdate=explode('/',$this->input->post('date'));
                $data=array(
                'scheme_id'     => $id,
                'remarks'       => $this->input->post('remarks'), 
                'amount_type'   => $this->input->post('amount_status'), 
                'cheque_number' => $this->input->post('cheque_number'), 
                'price'         => $this->input->post('amount'),
                'date'          => ($sdate[2].'-'.$sdate[0].'-'.$sdate[1])
                );
                
                $this->db->insert('land',$data);
                
                if($this->db->affected_rows()>0)
                {
                    $error='<div class="alert alert-success">Inserted!</div>';
                }
                else
                {
                    $error='<div class="alert alert-danger">Error. try again!</div>';
                }
            }
        }
        
        $receivedAmount=0;
        $totalMrlays=0;
        
        $s=$this->db->get_where('schemes',array('id'=>$id));
        foreach($s->result() as $scheme){}
        
        $p=$this->db->get_where('plots',array('scheme_id'=>$id));
        foreach($p->result() as $plotes)
        {
            $u=$this->db->get_where('users',array('plot_id'=>$plotes->id));
            
            foreach($u->result() as $user)
            {
                $receivedAmount += ($user->first_amount-$user->dealer_commision);
                
                $totalMrlays += ($user->marlay+($user->square_feets/225));
                
                $i=$this->db->get_where('instalment',array('plote_id'=>$plotes->id));
                
                foreach($i->result() as $ins)
                {
                   $receivedAmount += $ins->received_instalment;    
                }
            }
        }
        
        
        $this->db->select('land.*,sum(price) as sum');
        $this->db->where(array('scheme_id'=>$id));
        $sm=$this->db->get('land');
        
        foreach($sm->result() as $sum){}
        
        
        
        $this->db->order_by('date','asc');
        $query=$this->db->get_where('land',array('scheme_id'=>$id));
        
        $this->load->view('admin/view_land',array(
        'error'=>$error,
        'scheme'=>$scheme,
        'query'=>$query,
        'sum'=>$sum->sum,
        'receivedAmount'=>$receivedAmount,
        'totalMrlays'=>$totalMrlays));
	}
    public function ischeque()
    {
        $cheque_number=$this->input->post('cheque_number');
        $amount_status=$this->input->post('amount_status');
        
        if($amount_status=="Cash")
        {
            if(!empty($cheque_number))
            {
                $this->form_validation->set_message('ischeque','%s Must be empty.');
                return false;
            }
            return true;
        }
        else
        {
            if(empty($cheque_number))
            {
                $this->form_validation->set_message('ischeque','%s field is required.');
                return false;
            }
            return true;
        }
    }
    public function delete($id,$sid)
    {
        $this->db->where('id',intval($id));
        $this->db->delete('land');
        
        redirect(base_url('admin_land/index/'.$sid));
    }
    public function edit($id,$sid)
    {
        $error="";
        
        if($this->input->post('submit'))
        {
            $this->form_validation->set_rules('date','Date','required|xss_clean');
            $this->form_validation->set_rules('remarks','Remarks','required|xss_clean');
            $this->form_validation->set_rules('amount','Amount','required|xss_clean');
            $this->form_validation->set_rules('amount_status','Amount Type','required|xss_clean');
            $this->form_validation->set_rules('cheque_number','Cheque Number','xss_clean|callback_ischeque');
            
            if($this->form_validation->run() !== false)
            {
                $sdate=explode('/',$this->input->post('date'));
                $data=array(
                'remarks'       => $this->input->post('remarks'), 
                'amount_type'   => $this->input->post('amount_status'), 
                'cheque_number' => $this->input->post('cheque_number'), 
                'price'         => $this->input->post('amount'),
                'date'          => ($sdate[2].'-'.$sdate[0].'-'.$sdate[1])
                );
                
                $this->db->where('id',intval($id));
                $this->db->update('land',$data);
                
                if($this->db->affected_rows()>0)
                {
                    redirect(base_url('admin_land/index/'.$sid));
                }
                else
                {
                    $error='<div class="alert alert-danger">Error. try again!</div>';
                }
            }
        }
        
        $s=$this->db->get_where('schemes',array('id'=>$sid));
        foreach($s->result() as $scheme){}
        
        $d=$this->db->get_where('land',array('id'=>$id));
        foreach($d->result() as $land){}
        
        $this->load->view('admin/view_edit_land',array('error'=>$error,'scheme'=>$scheme,'land'=>$land));
    }
}