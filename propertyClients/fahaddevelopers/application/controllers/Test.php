<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Test extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('is_admin'))
        {
            redirect(base_url('admin_login'));
        }
    }
	public function index()
	{
	   $i=$this->db->get('instalmentt')->result();
       $rrrr=0;
       
       foreach($i as $r)
       {
          $this->db->where('id', $r->plote_id);
          $this->db->from('plots');
          
          if($this->db->count_all_results() > 0)
          {
            $data=array(
            'id'=>$r->id, 
            'plote_id'=>$r->plote_id, 
            'slip_number'=>$r->slip_number, 
            'received_instalment'=>$r->received_instalment, 
            'amount_status'=>$r->amount_status, 
            'date'=>$r->date, 
            'user_id'=>$r->user_id, 
            'user_cnic'=>$r->user_cnic
            );
            
            $this->db->insert('instalment',$data);
            
            if($this->db->affected_rows() == 0)
            {
                $rrrr++;
            }
          }
       }
       
       
       
       
       echo $rrrr;
	}
 }