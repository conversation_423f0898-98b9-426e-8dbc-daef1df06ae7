<div class="page-header card">
    <div class="container-fluid">
        <div class="pull-right">
            
        </div>
        <h1><?=($edit)?'Edit':'Add';?> Stripe Merchant</h1>
    </div>
</div>
<div class="container-fluid card">
  <div class="row">
    <?=$message;?>
  <form method="post" action="">
    <div class="form-group">
        <label for="name">Merchant Account Name</label>
        <input <?=$edit?'disabled':'';?> type="text" required="" value="<?=set_value('name',@$merchant->name);?>" class="form-control" name="name" id="name" aria-describedby="emailHelp" placeholder="Merchant Account Name">
    </div>
    <div class="form-group">
        <label for="limit">Merchant Limit Amount (Pre day)</label>
        <input type="number" required="" value="<?=set_value('json[limit]',@$json->limit);?>" class="form-control" name="json[limit]" id="limit" aria-describedby="emailHelp" placeholder="Merchant Limit Amount">
    </div>
    <div class="form-group">
        <label for="website">Website Approve on Stripe (Without https) exemple.com</label>
        <input type="text" class="form-control" value="<?=set_value('json[website]', @$json->website);?>" name="json[website]" id="website" placeholder="website">
    </div>
    <div class="form-group">
        <label for="PublishableKey">Stripe Publishable Key</label>
        <input  type="text" required="" class="form-control" value="<?=set_value('json[PublishableKey]', @$json->PublishableKey);?>" name="json[PublishableKey]" id="PublishableKey" placeholder="Stripe Publishable Key">
    </div>
    <div class="form-group">
        <label for="SecretKey">Stripe Secret Key</label>
        <input   type="text" required="" class="form-control" value="<?=set_value('json[SecretKey]', @$json->SecretKey);?>" name="json[SecretKey]" id="SecretKey" placeholder="Stripe Secret Key">
    </div>
    <button type="submit" class="btn btn-primary"><?=($edit)?'Update':'Submit';?></button>
    </form>
  </div>
</div>