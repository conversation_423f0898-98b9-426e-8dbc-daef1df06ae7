<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            
        </div>
        <h1>Add Paypal Merchant</h1>
        <ul class="breadcrumb">
            <li><a href="<?=site_url('home');?>">Home</a></li>
            <li><a class="active" href="<?=site_url('merchant');?>">Merchant List</a></li>
            <li><a class="active" href="<?=site_url('merchant/add/paypal');?>">Add Paypal Merchant</a></li>
        </ul>
    </div>
</div>
<div class="container-fluid">
  <div class="row">
    <?=$message;?>
  <form method="post" action="">
    <div class="form-group">
        <label for="name">Merchant Account Name</label>
        <input type="text" required="" value="<?=set_value('name',@$merchant->name);?>" class="form-control" name="name" id="name" aria-describedby="emailHelp" placeholder="Merchant Account Name">
    </div>
    <div class="form-group">
        <label for="limit">Merchant Limit Amount (Pre day)</label>
        <input type="number" required="" value="<?=set_value('json[limit]',@$json->limit);?>" class="form-control" name="json[limit]" id="limit" aria-describedby="emailHelp" placeholder="Merchant Limit Amount">
    </div>
    <div class="form-group">
        <label for="website">Website Approve on Paypal (Without https) exemple.com</label>
        <input type="text" class="form-control" value="<?=set_value('json[website]', @$json->website);?>" name="json[website]" id="website" placeholder="website">
    </div>
    <div class="form-group">
        <label for="businessEmail">Paypal Business Email</label>
        <input type="email" required="" class="form-control" value="<?=set_value('json[businessEmail]', @$json->businessEmail);?>" name="json[businessEmail]" id="businessEmail" placeholder="Paypal Business Email">
    </div>
    <div class="form-group">
        <label for="PAYPAL_CLIENT_ID">Paypal Api Client ID</label>
        <input type="text" required="" class="form-control" value="<?=set_value('json[PAYPAL_CLIENT_ID]', @$json->PAYPAL_CLIENT_ID);?>" name="json[PAYPAL_CLIENT_ID]" id="PAYPAL_CLIENT_ID" placeholder="Paypal Api Client ID">
    </div>
    <div class="form-group">
        <label for="PAYPAL_CLIENT_SECRET">Paypal Api Secret ID</label>
        <input type="text" required="" class="form-control" value="<?=set_value('json[PAYPAL_CLIENT_SECRET]', @$json->PAYPAL_CLIENT_SECRET);?>" name="json[PAYPAL_CLIENT_SECRET]" id="PAYPAL_CLIENT_SECRET" placeholder="Paypal Api Secret ID">
    </div>
    <button type="submit" class="btn btn-primary">Submit</button>
    </form>
  </div>
</div>