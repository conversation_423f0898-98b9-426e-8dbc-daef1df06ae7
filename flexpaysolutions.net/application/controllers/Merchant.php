<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Merchant extends CI_Controller {

	function __construct()
	{
		parent::__construct();
		if(!$this->session->has_userdata('user_id')){redirect('login');}
	}

	public function index()
	{
        $data['message'] = $this->session->flashdata('message');
        $this->db->order_by('merchant_id', 'desc');
        $data['merchants'] = $this->db->get_where('merchant')->result();

	    $data['page']="Merchant/merchant_index";
		$this->load->view('template', $data);
	}

    public function edit($name="stripe", $id=0)
    {

        $data['message']="";
        $data['edit']=true;
        if($_POST)
        {
            $update_data=array(
                //'merchant_name'=> $name,
                //'name' => trim($this->input->post('name')),
                'json' => json_encode($this->input->post('json')),
                'update_date'   => date('Y-m-d H:i:s')
            );
            $this->db->where('merchant_id',$id);
            $this->db->update('merchant', $update_data);

            $this->session->set_flashdata('message', '<div class="alert alert-success">Merchant account successfully Updated</div>');

            redirect('merchant');

        }

        $data['merchant'] = $this->db->get_where('merchant', array('merchant_id' => $id))->row();

        $data['json'] = json_decode($data['merchant']->json);

        // echo '<pre>';
        // print_r($data['json']);exit();

        switch($name)
        {
            case "stripe":
			{
                $data['page'] = "Merchant/Stripe/stripe_add_form";
				break;
			}
			case "paypal":
			{
                $data['page'] = "Merchant/Paypal/paypal_add_form";
				break;
			}
            case "bambora":
            {
                $data['page'] = "Merchant/Bambora/bambora_add_form";
				break;
            }
            case "eway":
            {
                $data['page'] = "Merchant/Eway/eway_add_form";
                break;
            }
            case "gocardless":
            {
                $data['page'] = "Merchant/Gocardless/gocardless_add_form";
                break;
            }
            case "paypro":
            {
                $data['page'] = "Merchant/Paypro/paypro_add_form";
                break;
            }
            case "authorizenet":
            {
                $data['page'] = "Merchant/Authorizenet/Authorizenet_add_form";
                break;
            }
            case "redsys":
            {
                $data['page'] = "Merchant/redsys/redsys_add_form";
                break;
            }
            case "converge":
            {
                $data['page'] = "Merchant/converge/converge_add_form";
                break;
            }
			default:
			{
				echo 'Invalid Url';
				exit();
			}
		}
        
		$this->load->view('template', $data);
    }

    public function add($name="stripe")
    {

        $data['message']="";
        $data['edit']=false;
        if($_POST)
        {

           $query = $this->db->get_where('merchant', array('name'=>trim($this->input->post('name'))));
           
           if($query->num_rows() > 0)
           {
            $data['message'] = '<div class="alert alert-danger">Merchant account name allready in use</div>';
           }
           else
           {
                $insert_data=array(
                    'merchant_name'=> $name,
                    'name' => trim($this->input->post('name')),
                    'json' => json_encode($this->input->post('json')),
                    'user_id' => $this->session->userdata('user_id'),
                    'created_date'   => date('Y-m-d H:i:s')
                );
                $this->db->insert('merchant', $insert_data);

                $this->session->set_flashdata('message', '<div class="alert alert-success">Merchant account successfully inserted</div>');

                redirect('merchant');
           }

        }

        switch($name)
        {
            case "stripe":
			{
                $data['page'] = "Merchant/Stripe/stripe_add_form";
				break;
			}
			case "paypal":
			{
                $data['page'] = "Merchant/Paypal/paypal_add_form";
				break;
			}
            case "bambora":
            {
                $data['page'] = "Merchant/Bambora/bambora_add_form";
				break;
            }
            case "eway":
            {
                $data['page'] = "Merchant/Eway/eway_add_form";
                break;
            }
            case "gocardless":
            {
                $data['page'] = "Merchant/Gocardless/gocardless_add_form";
                break;
            }
            case "paypro":
            {
                $data['page'] = "Merchant/Paypro/paypro_add_form";
                break;
            }
            case "authorizenet":
            {
                $data['page'] = "Merchant/Authorizenet/Authorizenet_add_form";
                break;
            }
            case "redsys":
            {
                $data['page'] = "Merchant/redsys/redsys_add_form";
                break;
            }
            case "converge":
            {
                $data['page'] = "Merchant/converge/converge_add_form";
                break;
            }
			default:
			{
				echo 'Invalid Url';
				exit();
			}
		}
        
		$this->load->view('template', $data);
    }
    public function delete($name="stripe", $merchant_id=0)
    {

        $this->db->where('merchant_id', $merchant_id);
        $this->db->delete('merchant');

        $this->session->set_flashdata('message', '<div class="alert alert-success">Merchant account successfully Deleted</div>');

        redirect('merchant/index/'.$name);
    }
	
}

