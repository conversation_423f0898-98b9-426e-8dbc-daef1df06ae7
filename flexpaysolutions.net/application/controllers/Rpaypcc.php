<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once('application/third_party/authorize-net/autoload.php');
use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;

class Rpaypcc extends CI_Controller {
	public function api()
	{
	   //file_put_contents('rashid.txt',$this->input->post('params'));exit();
	   
	   	$params=$this->input->post('params');
		//$params=file_get_contents('rashid.txt');
	   
	   	$params = str_replace(' ','+',$params);
		$params_get = $params;

		$params = base64_decode($params);
		$params = unserialize($params);
		
		$order_data = unserialize($params['order_data']);
		$order_items = unserialize($params['order_items']);
		$card_info = unserialize($params['card_info']);

		$parse_client_url=parse_url($params['client_url']);

		$website=$this->db->query("SELECT *,`websites`.user_id FROM `websites` INNER JOIN merchant on merchant.merchant_id = websites.merchant_id where websites.url=?",array($parse_client_url['host']))->row();
       
		if(isset($website))
		{
			$this->load->library($website->merchant_name);
			$this->{$website->merchant_name}->api($order_data,$order_items,$website,$params,$params_get,$card_info);
		}
	    
	}
}