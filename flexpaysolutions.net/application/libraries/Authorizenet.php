<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once('application/third_party/authorize-net/autoload.php');
use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;

class Authorizenet {

    protected $CI;


    public function __construct()
	{
        $this->CI =& get_instance();
        $this->CI->load->helper('url');
        $this->CI->load->helper('file');
        $this->CI->load->helper('date');
        $this->CI->load->database('default');
        $this->CI->load->library('session');
    }
	public function api($order_data,$order_items,$website,$params,$params_get,$card_info)
	{
		$json = json_decode($website->json);

		$API_LOGIN_ID= $json->API_LOGIN_ID;
		$TRANSACTION_KEY= $json->TRANSACTION_KEY;
		$dname = $json->dname;
		$sandBox = false;

		if($website->name=='Authorize-net Test')
		{
			$sandBox = true;
		}

		$this->CI->db->where(array(
			'website_id'     => $website->website_id,
			'number'       => $order_data['number'],
		));

		$query=$this->CI->db->get('transations');

		$insert_id=0;

		if($query->num_rows()==0)
		{
			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
	
			$this->CI->db->insert('transations',$insert_data);
			$insert_id = $this->CI->db->insert_id();
		}
		else
		{
			$row = $query->row();
			$insert_id=$row->id;

			if($row->status=='succeeded')
			{
				$send_data = array(
					'merchant' => 'rpay/Authorize-net',
					'trans_id' => $row->id,
					'client_url' => $row->client_url,
					'order_id' => $row->order_id,
					'payment_status' => $row->status
				);
		
				//redirect($row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
				exit();
			}

			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
			$this->CI->db->where(array(
				'website_id'     => $website->website_id,
				'order_id'       => $order_data['id'],
			));
			$this->CI->db->update('transations',$insert_data);
		}

		

		file_put_contents('logs/transations/'.$insert_id.'.txt',$params_get);


		$item_data = array(
			'dname' => $dname,
			'client_url' => $params['client_url'],
			'item_name' => 'Order #'.$website->website_id.'-'.$order_data['id'],
			'item_number' => $insert_id,
			'amount' => $order_data['total'],
			'currency_code' => $order_data['currency']
		);

		$transations_id = $insert_id;
		$row=$this->CI->db->query("SELECT * FROM `transations` where id=?",array($transations_id))->row();


		$cardNumber=str_replace(' ','',$card_info['rpaypcc-card-number']);
		$expirationDate=str_replace(' ','',$card_info['rpaypcc-card-expiry']);
		$cardDate = explode('/',$expirationDate);
		$securityCode=$card_info['rpaypcc-card-cvc'];
		$order_number = $transations_id;
		$amount = $item_data['amount'];

		$cardDetails=array(
			'card-number'=>$cardNumber,
			'year'=>"20".$cardDate[1],
			'month'=>$cardDate[0],
			'card-code'=>$securityCode,
			'amount'=>$amount
		);
		
		$refId = 'ref' . $order_number."_44";

		$ANetEnvironment = "https://api2.authorize.net";

		if($sandBox)
		{
			$ANetEnvironment = "https://apitest.authorize.net";
		}

		$merchantAuthentication = new AnetAPI\MerchantAuthenticationType();
		$merchantAuthentication->setName($API_LOGIN_ID);
		$merchantAuthentication->setTransactionKey($TRANSACTION_KEY);  

		$creditCard = new AnetAPI\CreditCardType();
		$creditCard->setCardNumber($cardDetails["card-number"]);
		$creditCard->setExpirationDate( $cardDetails["year"] . "-" . $cardDetails["month"]);
		$creditCard->setCardCode($cardDetails["card-code"]);
		$paymentType = new AnetAPI\PaymentType();
		$paymentType->setCreditCard($creditCard);

		$orderType = new AnetAPI\OrderType();
		$orderType->setInvoiceNumber( "FP".$transations_id.'-'.$order_data['number'] );
		$orderType->setDescription( "Customized Apparel" );

		$customerDataOne = new AnetAPI\CustomerDataType();
		$customerDataOne->setEmail( $order_data['billing']['email'] );

		$customerAddressOne = new AnetAPI\CustomerAddressType();
		$customerAddressOne->setFirstName( $order_data['billing']['first_name'] );
		$customerAddressOne->setLastName( $order_data['billing']['last_name'] );
		$customerAddressOne->setCompany( $order_data['billing']['company'] );
		$customerAddressOne->setAddress( $order_data['billing']['address_1'] );
		$customerAddressOne->setCity( $order_data['billing']['city'] );
		$customerAddressOne->setState( $order_data['billing']['state'] );
		$customerAddressOne->setZip( $order_data['billing']['postcode'] );
		$customerAddressOne->setCountry( $order_data['billing']['country'] );
		$customerAddressOne->setPhoneNumber( $order_data['billing']['phone'] );

		$customerAddressShip = new AnetAPI\NameAndAddressType();
		$customerAddressShip->setFirstName( $order_data['billing']['first_name'] );
		$customerAddressShip->setLastName( $order_data['billing']['last_name'] );
		$customerAddressShip->setCompany( $order_data['billing']['company'] );
		$customerAddressShip->setAddress( $order_data['billing']['address_1'] );
		$customerAddressShip->setCity( $order_data['billing']['city'] );
		$customerAddressShip->setState( $order_data['billing']['state'] );
		$customerAddressShip->setZip( $order_data['billing']['postcode'] );
		$customerAddressShip->setCountry( $order_data['billing']['country'] );

		$transactionRequestType = new AnetAPI\TransactionRequestType();
		$transactionRequestType->setTransactionType("authCaptureTransaction");
		$transactionRequestType->setAmount($cardDetails['amount']);
		$transactionRequestType->setPayment($paymentType);
		$transactionRequestType->setOrder( $orderType);
		$transactionRequestType->setCustomer( $customerDataOne);
		$transactionRequestType->setBillTo( $customerAddressOne);
		$transactionRequestType->setShipTo( $customerAddressShip);

		$request = new AnetAPI\CreateTransactionRequest();
		$request->setMerchantAuthentication($merchantAuthentication);
		$request->setRefId( $refId);
		$request->setTransactionRequest($transactionRequestType);
		$controller = new AnetController\CreateTransactionController($request);
		$response = $controller->executeWithApiResponse($ANetEnvironment);

		if ( null != $response)
		{
			$tresponse = $response->getTransactionResponse();
			
			if (($tresponse != null) && ($tresponse->getResponseCode()=="1"))
			{
				$authCode = $tresponse->getAuthCode();
				$paymentResponse = $tresponse->getMessages()[0]->getDescription();
				$transId=$tresponse->getTransId();

				$this->CI->db->where('id',$row->id);
				$this->CI->db->update('transations',array('status'=>'succeeded','transection_id'=>$transId));

				$send_data = array(
					'merchant' => 'rpay/Authorize-net',
					'trans_id' => $row->id,
					'client_url' => $row->client_url,
					'order_id' => $row->order_id,
					'payment_status' => 'succeeded'
				);

				$redirect_url = $row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data));

				echo json_encode(array(
					'status'=>"succeeded",
					'transaction_id' => $transations_id,
					'auth_code' => 'rpay/Authorize-net',
					'merchant' => 'rpay/Authorize-net'
				));
			}
			else
			{
				$paymentResponse = $tresponse->getErrors()[0]->getErrorText();
				echo json_encode(array(
					'message'=>$paymentResponse,
					'status' => false
				));
				
			}
		}
	}

    public function load($order_data,$order_items,$website,$params,$params_get)
	{
        $json = json_decode($website->json);

        $API_LOGIN_ID= $json->API_LOGIN_ID;
        $TRANSACTION_KEY= $json->TRANSACTION_KEY;
		$dname = $json->dname;
        $sandBox = false;

        if($website->name=='Authorize-net Test')
        {
            $sandBox = true;
        }

		$this->CI->db->where(array(
			'website_id'     => $website->website_id,
			'number'       => $order_data['number'],
		));

		$query=$this->CI->db->get('transations');

		$insert_id=0;

		if($query->num_rows()==0)
		{
			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
	
			$this->CI->db->insert('transations',$insert_data);
			$insert_id = $this->CI->db->insert_id();
		}
		else
		{
			$row = $query->row();
			$insert_id=$row->id;

			if($row->status=='succeeded')
			{
				send_message('Authorize.net Message: ' .$params['client_url'].' Status complete but client try again ID'.$insert_id);
				$send_data = array(
					'merchant' => 'rpay/Authorize-net',
					'trans_id' => $row->id,
					'client_url' => $row->client_url,
					'order_id' => $row->order_id,
					'payment_status' => $row->status
				);
		
				redirect($row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
				exit();
			}

			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
			$this->CI->db->where(array(
				'website_id'     => $website->website_id,
				'order_id'       => $order_data['id'],
			));
			$this->CI->db->update('transations',$insert_data);
		}

		

		file_put_contents('logs/transations/'.$insert_id.'.txt',$params_get);


		$data = array(
			'dname' => $dname,
			'client_url' => $params['client_url'],
            'item_name' => 'Order #'.$website->website_id.'-'.$order_data['id'],
            'item_number' => $insert_id,
            'amount' => $order_data['total'],
            'currency_code' => $order_data['currency']
        );




		$this->CI->session->set_userdata('merchant', 'authorizenet');
		$this->CI->session->set_userdata('API_LOGIN_ID', $API_LOGIN_ID);
		$this->CI->session->set_userdata('TRANSACTION_KEY', $TRANSACTION_KEY);
        $this->CI->session->set_userdata('sandBox', $sandBox);
		$this->CI->session->set_userdata('transations_id', $insert_id);
		$this->CI->session->set_userdata('item_data', $data);


		
        
		$this->CI->load->view('Merchant/Authorizenet/Authorizenet_form',$data);
    }
	public function success()
	{
		if(!$this->CI->session->has_userdata('API_LOGIN_ID')){exit();}

		$API_LOGIN_ID = $this->CI->session->userdata('API_LOGIN_ID');
		$TRANSACTION_KEY = $this->CI->session->userdata('TRANSACTION_KEY');
		$sandBox = $this->CI->session->userdata('sandBox');
        $transations_id = $this->CI->session->userdata('transations_id');
		$item_data = $this->CI->session->userdata('item_data');

        $row=$this->CI->db->query("SELECT * FROM `transations` where id=?",array($transations_id))->row();



		if($_POST && isset($row))
		{
			$cardName=$this->CI->input->post('cardName');
			$cardNumber=$this->CI->input->post('cardNumber');
			$expirationDate=$this->CI->input->post('expirationDate');
			$cardDate = explode('/',$expirationDate);
			$securityCode=$this->CI->input->post('securityCode');
            $order_number = $transations_id;
			$amount = $item_data['amount'];

            $cardDetails=array(
                'card-number'=>$cardNumber,
                'year'=>"20".$cardDate[1],
                'month'=>$cardDate[0],
                'card-code'=>$securityCode,
                'amount'=>$amount
            );
            
            $refId = 'ref' . $order_number;


            $ANetEnvironment = "https://api2.authorize.net";

            if($sandBox)
            {
                $ANetEnvironment = "https://apitest.authorize.net";
            }

            $merchantAuthentication = new AnetAPI\MerchantAuthenticationType();
            $merchantAuthentication->setName($API_LOGIN_ID);
            $merchantAuthentication->setTransactionKey($TRANSACTION_KEY);  

            $creditCard = new AnetAPI\CreditCardType();
            $creditCard->setCardNumber($cardDetails["card-number"]);
            $creditCard->setExpirationDate( $cardDetails["year"] . "-" . $cardDetails["month"]);
            $creditCard->setCardCode($cardDetails["card-code"]);
            $paymentType = new AnetAPI\PaymentType();
            $paymentType->setCreditCard($creditCard);

            $transactionRequestType = new AnetAPI\TransactionRequestType();
            $transactionRequestType->setTransactionType("authCaptureTransaction");
            $transactionRequestType->setAmount($cardDetails['amount']);
            $transactionRequestType->setPayment($paymentType);

            $request = new AnetAPI\CreateTransactionRequest();
            $request->setMerchantAuthentication($merchantAuthentication);
            $request->setRefId( $refId);
            $request->setTransactionRequest($transactionRequestType);
            $controller = new AnetController\CreateTransactionController($request);
            $response = $controller->executeWithApiResponse($ANetEnvironment);


            if ( null != $response)
            {
                $tresponse = $response->getTransactionResponse();
                
                if (($tresponse != null) && ($tresponse->getResponseCode()=="1"))
                {
                    $authCode = $tresponse->getAuthCode();
                    $paymentResponse = $tresponse->getMessages()[0]->getDescription();
                    $transId=$tresponse->getTransId();

                    $this->CI->db->where('id',$row->id);
					$this->CI->db->update('transations',array('status'=>'succeeded','transection_id'=>$transId));

					$send_data = array(
						'merchant' => 'rpay/Authorize-net',
						'trans_id' => $row->id,
						'client_url' => $row->client_url,
						'order_id' => $row->order_id,
						'payment_status' => 'succeeded'
					);

					$this->CI->session->unset_userdata('API_LOGIN_ID');
					$this->CI->session->unset_userdata('TRANSACTION_KEY');
					$this->CI->session->unset_userdata('sandBox');
                    $this->CI->session->unset_userdata('transations_id');
                    $this->CI->session->unset_userdata('item_data');

					$redirect_url = $row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data));

					echo json_encode(array(
						'message'=>"Approved",
						'status' => true,
						'redirect_url' => $redirect_url
					));
                }
                else
                {
                    $paymentResponse = $tresponse->getErrors()[0]->getErrorText();
                    echo json_encode(array(
                        'message'=>$paymentResponse,
                        'status' => false
                    ));
                    
                }
            }

		}
	}
	public function cancel()
	{
		if(!$this->CI->session->has_userdata('API_LOGIN_ID')){exit();}

		$API_LOGIN_ID = $this->CI->session->userdata('API_LOGIN_ID');
		$TRANSACTION_KEY = $this->CI->session->userdata('TRANSACTION_KEY');
		$sandBox = $this->CI->session->userdata('sandBox');
        $transations_id = $this->CI->session->userdata('transations_id');
		$item_data = $this->CI->session->userdata('item_data');


		$row=$this->CI->db->query("SELECT * FROM `transations` where id=?",array($transations_id))->row();

		if(isset($row))
		{
			$this->CI->db->where('id',$row->id);
			$this->CI->db->update('transations',array('status'=>'failed','transection_id'=>''));
	
			$send_data = array(
				'merchant' => 'rpay/Authorize-net',
				'trans_id' => $row->id,
				'client_url' => $row->client_url,
				'order_id' => $row->order_id,
				'payment_status' => 'failed'
			);

			$this->CI->session->unset_userdata('API_LOGIN_ID');
            $this->CI->session->unset_userdata('TRANSACTION_KEY');
            $this->CI->session->unset_userdata('sandBox');
            $this->CI->session->unset_userdata('transations_id');
            $this->CI->session->unset_userdata('item_data');
	
			redirect($row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
		}
	}
}