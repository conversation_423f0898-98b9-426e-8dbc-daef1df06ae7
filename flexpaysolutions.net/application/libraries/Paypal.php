<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Paypal {

    protected $CI;
    protected $PAYPAL_URL;
    protected $PAYPAL_ID = "";


    public function __construct()
	{
        $this->CI =& get_instance();
        $this->CI->load->helper('url');
        $this->CI->load->helper('file');
        $this->CI->load->helper('date');
        $this->CI->load->database('default');

        $this->PAYPAL_URL="https://www.paypal.com/cgi-bin/webscr";
    }

    public function some_method()
    {
        redirect('home');
    }
	public function api($order_data,$order_items,$website,$params,$params_get,$card_info)
	{
		$PAYPAL_SANDBOX = false;
		
		$json = json_decode($website->json);
		$businessEmail = $json->businessEmail;
		$clientId = $json->PAYPAL_CLIENT_ID;
		$clientSecret = $json->PAYPAL_CLIENT_SECRET;

		$paypalApiUrl = 'https://api-m.paypal.com';

		if($PAYPAL_SANDBOX)
		{
			$paypalApiUrl = 'https://api-m.sandbox.paypal.com';
		}

		$websiteKey = $json->website;
		$limit = $json->limit;
		
		$check=$this->CI->db->query("SELECT IFNULL(sum(amount),0) as amount FROM `transations` where status='succeeded' and merchant_id=".$website->merchant_id." and DATE(created_date)='".date('Y-m-d')."'")->row();
        
		if(($check->amount+$order_data['total']) > $limit)
		{
			echo json_encode([
				'status' => false,
				'message' => "Somthing Wrong"
			]);
			exit();
		}

		$this->CI->db->where(array(
			'website_id'     => $website->website_id,
			'number'       => $order_data['number'],
		));

		$query=$this->CI->db->get('transations');

		$insert_id=0;

		if($query->num_rows()==0)
		{
			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
	
			$this->CI->db->insert('transations',$insert_data);
			$insert_id = $this->CI->db->insert_id();
		}
		else
		{
			$row = $query->row();
			$insert_id=$row->id;

			if($row->status=='succeeded')
			{
				$send_data = array(
					'merchant' => 'rpay/PayPal-Card',
					'trans_id' => $row->id,
					'client_url' => $row->client_url,
					'order_id' => $row->order_id,
					'payment_status' => $row->status
				);
		
				//redirect($row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
				exit();
			}

			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
			$this->CI->db->where(array(
				'website_id'     => $website->website_id,
				'order_id'       => $order_data['id'],
			));
			$this->CI->db->update('transations',$insert_data);
		}

		

		file_put_contents('logs/transations/'.$insert_id.'.txt',$params_get);


		// ✅ Step 1: Get Access Token
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $paypalApiUrl."/v1/oauth2/token");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_USERPWD, $clientId . ":" . $clientSecret);
		curl_setopt($ch, CURLOPT_POSTFIELDS, "grant_type=client_credentials");
		curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type: application/x-www-form-urlencoded"]);

		$result = curl_exec($ch);
		curl_close($ch);

		$response = json_decode($result, true);
		$accessToken = $response['access_token'] ?? null;

		if (!$accessToken) {
			echo json_encode(['status' => false, 'message' => 'Failed to get PayPal access token']);
			exit;
		}

		// ✅ Billing Information
		$billingInfo = [
			"first_name" => $order_data['billing']['first_name'],
			"last_name" => $order_data['billing']['last_name'],
			"email" => $order_data['billing']['email'],
			"phone" => $order_data['billing']['phone'],
			"address_line_1" => $order_data['billing']['address_1'],
			"address_line_2" => $order_data['billing']['address_2'],
			"city" => $order_data['billing']['city'],
			"state" => $order_data['billing']['state'],
			"zip" => $order_data['billing']['postcode'],
			"country" => $order_data['billing']['country']
		];

		// ✅ Step 2: Create an Order
		$orderData = [
			"intent" => "CAPTURE",
			"purchase_units" => [
				[
					"amount" => [
						"currency_code" => strtoupper($order_data['currency']),
						"value" => $order_data['total']
					],
					"invoice_id" => "FP".$transations_id.'-'.$order_data['number']
				]
			],
			"payer" => [
				"name" => [
					"given_name" => $billingInfo['first_name'],
					"surname" => $billingInfo['last_name']
				],
				"email_address" => $billingInfo['email'],
				"phone" => [
					"phone_number" => [
						"national_number" => $billingInfo['phone']
					]
				],
				"address" => [
					"address_line_1" => $billingInfo['address_line_1'],
					"address_line_2" => $billingInfo['address_line_2'],
					"admin_area_2" => $billingInfo['city'],
					"admin_area_1" => $billingInfo['state'],
					"postal_code" => $billingInfo['zip'],
					"country_code" => $billingInfo['country']
				]
			]
		];

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $paypalApiUrl."/v2/checkout/orders");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			"Content-Type: application/json",
			"Authorization: Bearer " . $accessToken,
			"PayPal-Request-Id: " . uniqid()
		]);

		$orderResponse = curl_exec($ch);
		curl_close($ch);
		$order = json_decode($orderResponse, true);
		$orderId = $order['id'] ?? null;

		if (!$orderId) {
			echo json_encode(['status' => false, 'message' => 'Failed to create PayPal order']);
			exit;
		}

		// ✅ Step 3: Capture Payment
		$cardNumber=str_replace(' ','',$card_info['rpaypcc-card-number']);

		$expirationDate=str_replace(' ','',$card_info['rpaypcc-card-expiry']);
		$cardDate = explode('/',$expirationDate);
		$securityCode=$card_info['rpaypcc-card-cvc'];


		$cardExpiry = "20".$cardDate[1]."-".$cardDate[0]; 
		$cardCVV = $securityCode; 

		$paymentData = [
			"payment_source" => [
				"card" => [
					"number" => $cardNumber,
					"expiry" => $cardExpiry,
					"security_code" => $cardCVV
				]
			]
		];

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $paypalApiUrl."/v2/checkout/orders/$orderId/capture");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			"Content-Type: application/json",
			"Authorization: Bearer " . $accessToken
		]);

		$paymentResponse = curl_exec($ch);
		curl_close($ch);
		$payment = json_decode($paymentResponse, true);

		// ✅ Extract Payment Status
		if (isset($payment['status']) && $payment['status'] === 'COMPLETED') {
			$transactionId = $payment['purchase_units'][0]['payments']['captures'][0]['id'] ?? null;

			$this->CI->db->where('id',$row->id);
			$this->CI->db->update('transations',array('status'=>'succeeded','transection_id'=>$transactionId));

			echo json_encode([
				'status' => "succeeded",
				'transaction_id' => $transactionId,
				'auth_code' => 'rpay/Paypal',
				'merchant' => 'rpay/Paypal'
			]);
		} else {
			// Extract Error Message
			$errorMessage = "Payment failed"; 

			if (isset($payment['details'][0]['description'])) {
				$errorMessage = $payment['details'][0]['description'];
			} elseif (isset($payment['message'])) {
				$errorMessage = $payment['message'];
			}

			$this->CI->db->where('id',$row->id);
			$this->CI->db->update('transations',array('status'=>'failed'));

			echo json_encode([
				'status' => false,
				'message' => $errorMessage
			]);
		}
        
	}

    public function load($order_data,$order_items,$website,$params,$params_get)
	{
		$PAYPAL_SANDBOX = false;
		
		$json = json_decode($website->json);
		$businessEmail = $json->businessEmail;
		$PAYPAL_CLIENT_ID = $json->PAYPAL_CLIENT_ID;
		$PAYPAL_CLIENT_SECRET = $json->PAYPAL_CLIENT_SECRET;

		$websiteKey = $json->website;
		$limit = $json->limit;
		
		$check=$this->CI->db->query("SELECT IFNULL(sum(amount),0) as amount FROM `transations` where status='succeeded' and merchant_id=".$website->merchant_id." and DATE(created_date)='".date('Y-m-d')."'")->row();
        
		if(($check->amount+$order_data['total']) > $limit)
		{
			header("Location: " . $params['client_url']);
			exit();
		}

        if(!empty($websiteKey) && $_SERVER['HTTP_HOST'] != $websiteKey)
		{
			$actual_link = "https://".$websiteKey.$_SERVER['REQUEST_URI'];
			redirect($actual_link );
			exit();
		}

		
		if($website->name=="Paypal Test")
		{
			$PAYPAL_SANDBOX = true;
			$this->PAYPAL_URL="https://www.sandbox.paypal.com/cgi-bin/webscr";
			$PAYPAL_CLIENT_ID = 'Aek50TuXPDFLw1Np_Jg7PjcwTHcxQowmNsiSsBvm88xm5SF3nDj8T7TsDb_-qiB9hnwLinpI6yyWhNoj';
		    $PAYPAL_CLIENT_SECRET = 'EL_PiMdtmrIkoBk2cchh7gA19vdmXQPNOEdzK_Wsi3tI6_I3sb7M-1as-QvylNzXHg4d0ZhIIhSj4ahw';
		}

		$this->CI->db->where(array(
			'website_id'     => $website->website_id,
			'number'       => $order_data['number'],
		));

		$query=$this->CI->db->get('transations');

		$insert_id=0;

		if($query->num_rows()==0)
		{
			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
	
			$this->CI->db->insert('transations',$insert_data);
			$insert_id = $this->CI->db->insert_id();
		}
		else
		{
			$row = $query->row();
			$insert_id=$row->id;

			if($row->status=='succeeded')
			{
				send_message('Paypal Message: ' .$params['client_url'].' Status complete but client try again ID'.$insert_id);
				$send_data = array(
					'merchant' => 'rpay/Paypal',
					'trans_id' => $row->id,
					'client_url' => $row->client_url,
					'order_id' => $row->order_id,
					'payment_status' => $row->status
				);
		
				redirect($row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
				exit();
			}

			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
			$this->CI->db->where(array(
				'website_id'     => $website->website_id,
				'order_id'       => $order_data['id'],
			));
			$this->CI->db->update('transations',$insert_data);
		}

		file_put_contents('logs/transations/'.$insert_id.'.txt',$params_get);

		$itemName = $insert_id;//$website->website_id.'-'.$order_data['id'];

		$data = array(
			'PAYPAL_SANDBOX'=>$PAYPAL_SANDBOX,
			'PAYPAL_CLIENT_ID'=>$PAYPAL_CLIENT_ID,
			'PAYPAL_CLIENT_SECRET'=>$PAYPAL_CLIENT_SECRET,
            'business' => $businessEmail,
            'cmd'      => '_xclick',
            'item_name' => $itemName,
            'item_number' => $insert_id,
            'amount' => $order_data['total'],
            'currency_code' => $order_data['currency'],
            'return' => site_url('payment/success'),
            'cancel_return' => site_url('payment/cancel'),
			'notify_url' => site_url('payment/notify'),
			'PAYPAL_URL' =>$this->PAYPAL_URL
        );

		$paypalUrl = $this->PAYPAL_URL."?" . http_build_query([
			'cmd'           => '_xclick',
			'business'      => $businessEmail,
			'item_name'     => $itemName,
			'amount'        => $order_data['total'],
			'currency_code' => $order_data['currency'],
			'return' => site_url('payment/success/'.$insert_id),
			'notify_url' => site_url('payment/notify/'.$insert_id),
            'cancel_return' => site_url('payment/cancel/'.$insert_id)
		]);
		
		// Redirect to PayPal
		header("Location: $paypalUrl");
        
		//$this->CI->load->view('Merchant/Paypal/paypal_form',$data);
        
	}
	public function success($transation="",$merchant="")
	{
		$json = json_decode($merchant->json);

		$businessEmail = $json->businessEmail;
		$PAYPAL_SANDBOX = false;
		$PAYPAL_CLIENT_ID = $json->PAYPAL_CLIENT_ID;
		$PAYPAL_CLIENT_SECRET = $json->PAYPAL_CLIENT_SECRET;

		if($merchant->name=="Paypal Test")
		{
			$PAYPAL_SANDBOX = true;
			$this->PAYPAL_URL="https://www.sandbox.paypal.com/cgi-bin/webscr";
			$PAYPAL_CLIENT_ID = 'Aek50TuXPDFLw1Np_Jg7PjcwTHcxQowmNsiSsBvm88xm5SF3nDj8T7TsDb_-qiB9hnwLinpI6yyWhNoj';
		    $PAYPAL_CLIENT_SECRET = 'EL_PiMdtmrIkoBk2cchh7gA19vdmXQPNOEdzK_Wsi3tI6_I3sb7M-1as-QvylNzXHg4d0ZhIIhSj4ahw';
		}

		$transations_id = $transation->id;

	    if(!empty($_GET['item_number']) && !empty($_GET['tx']) && !empty($_GET['amt'])){ 
			// Get transaction information from URL 
			$item_number = $_GET['item_number'];  
			$txn_id = $_GET['tx']; 
			$payment_gross = $_GET['amt']; 
			$currency_code = $_GET['cc']; 
			$payment_status = $_GET['st']; 

			if($payment_status=='Completed')
			{
				$this->CI->db->where('id',$row->id);
				$this->CI->db->update('transations',array('status'=>'succeeded','transection_id'=>$txn_id));

				$send_data = array(
					'merchant' => 'rpay/Paypal',
					'trans_id' => $transation->id,
					'client_url' => $transation->client_url,
					'order_id' => $transation->order_id,
					'payment_status' => 'succeeded'
				);

				

				redirect($transation->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));

			}
			else
			{
				$this->CI->db->where('id',$transation->id);
				$this->CI->db->update('transations',array('status'=>'failed','transection_id'=>''));

				$send_data = array(
					'merchant' => 'rpay/Paypal',
					'trans_id' => $transation->id,
					'client_url' => $transation->client_url,
					'order_id' => $transation->order_id,
					'payment_status' => 'failed'
				);

				redirect($row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));

			}
			 
			
		} 
	}
	public function cancel($transation="",$merchant="")
	{
		$this->CI->db->where('id',$transation->id);
		$this->CI->db->update('transations',array('status'=>'failed','transection_id'=>''));

		$send_data = array(
			'merchant' => 'rpay/Paypal',
			'trans_id' => $transation->id,
			'client_url' => $transation->client_url,
			'order_id' => $transation->order_id,
			'payment_status' => 'failed'
		);

		redirect($transation->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
	}
	public function notify($transation="",$merchant="")
	{
		$raw_post_data = file_get_contents('php://input'); 
		$raw_post_array = explode('&', $raw_post_data); 
		$myPost = array(); 
		foreach ($raw_post_array as $keyval) { 
			$keyval = explode ('=', $keyval); 
			if (count($keyval) == 2) 
				$myPost[$keyval[0]] = urldecode($keyval[1]); 
		} 

		file_put_contents('logs/paypal/'.$myPost['txn_id'].'.txt',json_encode($myPost));

		$transations_id = $myPost['item_number'];
		$payment_status = $myPost['payment_status'];
		$txn_id = $myPost['txn_id'];

		if($payment_status=='Completed')
		{
			$this->CI->db->where('id',$row->id);
			$this->CI->db->update('transations',array('status'=>'succeeded','transection_id'=>$txn_id));

			$send_data = array(
				'merchant' => 'rpay/Paypal/notify',
				'trans_id' => $transation->id,
				'client_url' => $transation->client_url,
				'order_id' => $transation->order_id,
				'payment_status' => 'succeeded'
			);

			$url = $row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data));
			fetch_url_content($url);
		}

	}


	public function checkout_init()
	{
		if(!$this->CI->session->has_userdata('businessEmail')){exit();}
		require_once('application/third_party/paypal/PaypalCheckout.class.php');
		$businessEmail = $this->CI->session->userdata('businessEmail');
		$PAYPAL_SANDBOX = $this->CI->session->userdata('PAYPAL_SANDBOX');
		$PAYPAL_CLIENT_ID = $this->CI->session->userdata('PAYPAL_CLIENT_ID');
		$PAYPAL_CLIENT_SECRET = $this->CI->session->userdata('PAYPAL_CLIENT_SECRET');
		$transations_id = $this->CI->session->userdata('transations_id');
		$itemName = $this->CI->session->userdata('itemName');

		$row=$this->CI->db->query("SELECT * FROM `transations` where id=?",array($transations_id))->row();

		if(isset($row))
		{
			$paypal = new PaypalCheckout($PAYPAL_CLIENT_ID,$PAYPAL_CLIENT_SECRET,$PAYPAL_SANDBOX); 
 
			$response = array('status' => 0, 'msg' => 'Request Failed!'); 
			$api_error = ''; 
			if(!empty($_POST['request_type']) && $_POST['request_type'] == 'create_order'){
				$payment_source = $_POST['payment_source']; 
			 
				$product_data = array( 
					'item_number' => $row->id, 
					'item_name' => $itemName, 
					'price' => $row->amount, 
					'currency' => $row->currency, 
				); 
			 
				// Create order with PayPal Orders API 
				try { 
					$order = $paypal->createOrder($product_data, $payment_source); 
				} catch(Exception $e) {  
					$api_error = $e->getMessage();  
				} 
				 
				if(!empty($order)){ 
					$response = array( 
						'status' => 1,  
						'data' => $order 
					); 
				}else{ 
					$response['msg'] = $api_error; 
				} 
			}elseif(!empty($_POST['request_type']) && $_POST['request_type'] == 'capture_order'){
				$order_id = $_POST['order_id']; 
			 
				// Create order with PayPal Orders API 
				try { 
					$order = $paypal->captureOrder($order_id); 
				} catch(Exception $e) {  
					$api_error = $e->getMessage();  
				} 
				 
				if(!empty($order)){ 
					$order_id = $order['id']; 
					$order_status = $order['status']; 
			 
					$payment_source = $payment_source_card_name = $payment_source_card_last_digits = $payment_source_card_expiry = $payment_source_card_brand = $payment_source_card_type = ''; 
					if(!empty($order['payment_source'])){ 
						foreach($order['payment_source'] as $key=>$value){ 
							$payment_source = $key; 
							if($payment_source == 'card'){ 
								$payment_source_card_name = @$value['name']; 
								$payment_source_card_last_digits = $value['last_digits']; 
								$payment_source_card_expiry = $value['expiry']; 
								$payment_source_card_brand = $value['brand']; 
								$payment_source_card_type = $value['type']; 
							} 
						} 
					} 
			 
					if(!empty($order['purchase_units'][0])){ 
						$purchase_unit = $order['purchase_units'][0]; 
						if(!empty($purchase_unit['payments'])){ 
							$payments = $purchase_unit['payments']; 
							if(!empty($payments['captures'])){ 
								$captures = $payments['captures']; 
								if(!empty($captures[0])){ 
									$txn_id = $captures[0]['id']; 
									$payment_status = $captures[0]['status']; 
									$custom_id = $captures[0]['custom_id']; 
									$amount_value = $captures[0]['amount']['value']; 
									$currency_code = $captures[0]['amount']['currency_code']; 
									$create_time = date("Y-m-d H:i:s", strtotime($captures[0]['create_time'])); 
								} 
							} 
						} 
					} 
			 
					if(!empty($order_id) && $order_status == 'COMPLETED'){
						$this->CI->session->unset_userdata('businessEmail');
						$this->CI->session->unset_userdata('PAYPAL_SANDBOX');
						$this->CI->session->unset_userdata('PAYPAL_CLIENT_ID');
						$this->CI->session->unset_userdata('PAYPAL_CLIENT_SECRET');
						$this->CI->session->unset_userdata('transations_id');
						
						$this->CI->db->where('id',$row->id);
						$this->CI->db->update('transations',array('status'=>'succeeded','transection_id'=>$txn_id));

						$send_data = array(
							'merchant' => 'rpay/PaypalPro',
							'trans_id' => $row->id,
							'client_url' => $row->client_url,
							'order_id' => $row->order_id,
							'payment_status' => 'succeeded'
						);

						$ref_id_enc = $row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data));


						$response = array('status' => 1, 'msg' => 'Transaction completed!', 'ref_id' => $ref_id_enc);
					} 
				}else{ 
					$response['msg'] = $api_error; 
				} 
			} 
			echo json_encode($response); 
		}
	}
}