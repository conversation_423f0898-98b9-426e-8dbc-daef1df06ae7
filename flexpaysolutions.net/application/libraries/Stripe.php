<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once('application/third_party/stripe/autoload.php');

class Stripe {

    protected $CI;

    public function __construct()
	{
        $this->CI =& get_instance();
        $this->CI->load->helper('url');
        $this->CI->load->helper('file');
        $this->CI->load->helper('date');
        $this->CI->load->database('default');
        $this->CI->load->library('session');
    }

    public function some_method()
    {
        redirect('home');
    }

    public function load($order_data,$order_items,$website,$params,$params_get)
	{
		$json = json_decode($website->json);
		
		$PublishableKey = $json->PublishableKey;
		$stripeSecretKey = $json->SecretKey;

		$websiteKey = $json->website;
		$limit = $json->limit;
		
		$check=$this->CI->db->query("SELECT IFNULL(sum(amount),0) as amount FROM `transations` where status='succeeded' and merchant_id=".$website->merchant_id." and DATE(created_date)='".date('Y-m-d')."'")->row();
        
		if(($check->amount+$order_data['total']) > $limit)
		{
			header("Location: " . $params['client_url']);
			exit();
		}

        if(!empty($websiteKey) && $_SERVER['HTTP_HOST'] != $websiteKey)
		{
			$actual_link = "https://".$websiteKey.$_SERVER['REQUEST_URI'];
			redirect($actual_link );
			exit();
		}

		$this->CI->db->where(array(
			'website_id'     => $website->website_id,
			'number'       => $order_data['number'],
		));

		$query=$this->CI->db->get('transations');

		$insert_id=0;

		if($query->num_rows()==0)
		{
			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
	
			$this->CI->db->insert('transations',$insert_data);
			$insert_id = $this->CI->db->insert_id();
		}
		else
		{
			$row = $query->row();
			$insert_id=$row->id;

			if($row->status=='succeeded')
			{
				send_message('Eway Message: ' .$params['client_url'].' Status complete but client try again ID'.$insert_id);
				$send_data = array(
					'merchant' => 'rpay/Stripe',
					'trans_id' => $row->id,
					'client_url' => $row->client_url,
					'order_id' => $row->order_id,
					'payment_status' => $row->status
				);
		
				redirect($row->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
				exit();
			}

			$insert_data=array(
				'status'         => 'pending',
				'user_id'        => $website->user_id,
				'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
				'client_email'   => $order_data['billing']['email'],
				'currency'       => strtoupper($order_data['currency']),
				'amount'         => $order_data['total'] ,
				'order_id'       => $order_data['id'],
				'number'         => $order_data['number'],
				'client_url'     => $params['client_url'],
				'merchant_id'    => $website->merchant_id,
				'merchant_name'  => $website->merchant_name,
				'merchant_title' => $website->name,
				'section_id'     => '',
				'website_id'     => $website->website_id,
				'created_date'   => date('Y-m-d H:i:s')
			);
			$this->CI->db->where(array(
				'website_id'     => $website->website_id,
				'order_id'       => $order_data['id'],
			));
			$this->CI->db->update('transations',$insert_data);
		}

		file_put_contents('logs/transations/'.$insert_id.'.txt',$params_get);
		
		try {
			
			\Stripe\Stripe::setApiKey($stripeSecretKey);

			$checkout_session = \Stripe\Checkout\Session::create([
				'customer_email' => $order_data['billing']['email'],
				'line_items' => [[
				  'price_data' => [
					  'product_data' => ['name' => 'Order #: P'.$website->user_id.'-'.$order_data['number']],
					  'unit_amount' => intval($order_data['total']*100),
					  'currency' => $order_data['currency'],
					],
				  'quantity' => 1,
				]],
				'metadata' => array(
				  'OrderId' => $order_data['number'],
				  'FirstName' => $order_data['billing']['first_name'],
				  'LastName' => $order_data['billing']['last_name'],
				  'Address' => $order_data['billing']['address_1'],
				  'City' =>$order_data['billing']['city'],
				  'Province' => $order_data['billing']['state'],
				  'PostalCode' => $order_data['billing']['postcode'],
				  'Country' => $order_data['billing']['country'],
				  'Email' => $order_data['billing']['email'],
				  'Phone' => $order_data['billing']['phone'],
			  ),
				'mode' => 'payment',
				'success_url' => site_url('payment/success/'.$insert_id.'?session_id={CHECKOUT_SESSION_ID}'),
				'cancel_url' => site_url('payment/cancel/'.$insert_id.'?session_id={CHECKOUT_SESSION_ID}'),
				'automatic_tax' => [
				  'enabled' => true,
				],
			  ]);
		  }
		  catch(Exception $e) {
			send_message('Stripe Message: ' .$params['client_url'].' '.$e->getMessage());
			header("Location: " . $params['client_url']);
			exit();
		  }
        
        header("HTTP/1.1 303 See Other");
        header("Location: " . $checkout_session->url);
	}
	public function success($transation="",$merchant="")
	{
		$json = json_decode($merchant->json);

		$stripeSecretKey = $json->SecretKey;
		$transations_id = $transation->id;

		$stripe = new \Stripe\StripeClient($stripeSecretKey);

		if(!isset($_GET['session_id'])){exit();}

		$session = $stripe->checkout->sessions->retrieve($_GET['session_id']);

		if($session['payment_status']=='paid')
		{
			$this->CI->db->where('id',$transations_id);
			$this->CI->db->update('transations',array('status'=>'succeeded','transection_id'=>$session['payment_intent']));

			$send_data = array(
				'merchant' => 'rpay/Stripe',
				'trans_id' => $transations_id,
				'client_url' => $transation->client_url,
				'order_id' => $transation->order_id,
				'payment_status' => 'succeeded'
			);

			redirect($transation->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
		}
		else
		{
			$this->CI->db->where('id',$transations_id);
			$this->CI->db->update('transations',array('status'=>'failed','transection_id'=>$session['payment_intent']));

			$send_data = array(
				'merchant' => 'rpay/Stripe',
				'trans_id' => $transations_id,
				'client_url' => $transation->client_url,
				'order_id' => $transation->order_id,
				'payment_status' => 'failed'
			);

			redirect($transation->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
		}
	}
	public function cancel($transation="",$merchant="")
	{
		$json = json_decode($merchant->json);

		$stripeSecretKey = $json->SecretKey;
		$transations_id = $transation->id;

		$stripe = new \Stripe\StripeClient($stripeSecretKey);

		$session = $stripe->checkout->sessions->retrieve($_GET['session_id']);

		$this->CI->db->where('id',$transations_id);
		$this->CI->db->update('transations',array('status'=>'failed','transection_id'=>$session['payment_intent']));

		$send_data = array(
			'merchant' => 'rpay/Stripe',
			'trans_id' => $transations_id,
			'client_url' => $transation->client_url,
			'order_id' => $transation->order_id,
			'payment_status' => 'failed'
		);

		$this->CI->session->unset_userdata('stripe_SecretKey');

		redirect($transation->client_url.'/wc-api/rpay_callback?params='.base64_encode(serialize($send_data)));
	}
}