<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Log extends CI_Log {
    protected $CI;

    public function __construct() {
        parent::__construct();
    }

    private function get_ci_instance() {
        if ($this->CI === null) {
            $this->CI =& get_instance();
        }
        return $this->CI;
    }

    public function write_log($level, $msg) {
        if (parent::write_log($level, $msg)) {
            $this->log_to_database($level, $msg);
            return true;
        }
        return false;
    }

    private function log_to_database($level, $msg) {
        $ci = $this->get_ci_instance();
        $data = [
            'level' => $level,
            'message' => $msg,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        $ci->db->insert('error_logs', $data);
    }
}