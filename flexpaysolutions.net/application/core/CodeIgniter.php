<?php
// Load CodeIgniter's database library
require_once APPPATH . 'config/database.php';
$CI =& get_instance();
$CI->load->database();

// Custom error handler
function custom_error_handler($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        // This error code is not included in error_reporting
        return;
    }

    global $CI;
    $data = [
        'error_type' => 'Error',
        'error_message' => $message,
        'file_path' => $file,
        'line_number' => $line,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    $CI->db->insert('php_error_logs', $data);
    return true;
}

// Custom exception handler
function custom_exception_handler($exception) {
    global $CI;
    $data = [
        'error_type' => 'Exception',
        'error_message' => $exception->getMessage(),
        'file_path' => $exception->getFile(),
        'line_number' => $exception->getLine(),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    $CI->db->insert('php_error_logs', $data);
}

// Custom shutdown function to catch fatal errors
function custom_shutdown_handler() {
    $error = error_get_last();
    if ($error !== NULL && $error['type'] === E_ERROR) {
        global $CI;
        $data = [
            'error_type' => 'Fatal Error',
            'error_message' => $error['message'],
            'file_path' => $error['file'],
            'line_number' => $error['line'],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        $CI->db->insert('php_error_logs', $data);
    }
}

// Set the custom handlers
set_error_handler('custom_error_handler');
set_exception_handler('custom_exception_handler');
register_shutdown_function('custom_shutdown_handler');