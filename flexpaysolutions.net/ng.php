<?php

$apiKey = "sk_GKauT7zpHL4z3zkMclkJDHby2YfeEs3iX7iHSMiGQNQE1OLboVIgnkOE_1Zyz11v"; // Use your actual API key

$curl = curl_init();

curl_setopt_array($curl, array(
    CURLOPT_URL => "https://sandbox-merchant.revolut.com/api/1.0/payment-methods",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode([
        "type" => "card",
        "details" => [
            "card_number" => "****************", // Test Visa card
            "expiry_month" => 12,
            "expiry_year" => 2025,
            "security_code" => "123"
        ]
    ]),
    CURLOPT_HTTPHEADER => array(
        "Authorization: Bearer $apiKey",
        "Content-Type: application/json"
    ),
));

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

echo "HTTP Code: " . $httpCode . "\n";
echo "Response: " . $response;
?>