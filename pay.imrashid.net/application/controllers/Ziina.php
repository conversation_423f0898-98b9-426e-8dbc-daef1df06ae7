<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Ziina Payment Controller
 * 
 * Handles Ziina payment processing requests
 * 
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

require_once(APPPATH . 'ziina/index.php');

class Ziina extends CI_Controller {
    
    private $ziina_api;
    
    public function __construct() {
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->database();
        
        // Load Ziina libraries
        require_once(APPPATH . 'ziina/libraries/ZiinaAPI.php');
        require_once(APPPATH . 'ziina/libraries/ZiinaPayment.php');
    }
    
    /**
     * Main API endpoint for payment processing
     */
    public function api() {
        try {
            // Get parameters from POST request
            $params = $this->input->post('params');
            
            if (!$params) {
                $this->_sendErrorResponse('Missing parameters');
                return;
            }
            
            // Decode parameters
            $params = str_replace(' ', '+', $params);
            $params_get = $params;
            $params = base64_decode($params);
            $params = unserialize($params);
            
            if (!$params) {
                $this->_sendErrorResponse('Invalid parameters');
                return;
            }
            
            // Extract data
            $order_data = unserialize($params['order_data']);
            $order_items = unserialize($params['order_items']);
            $card_info = isset($params['card_info']) ? unserialize($params['card_info']) : null;
            
            // Get client URL and find website configuration
            $parse_client_url = parse_url($params['client_url']);
            $website = $this->db->query(
                "SELECT *, `websites`.user_id FROM `websites` 
                 INNER JOIN merchant ON merchant.merchant_id = websites.merchant_id 
                 WHERE websites.url = ?", 
                array($parse_client_url['host'])
            )->row();
            
            if (!isset($website)) {
                $this->_sendErrorResponse('Website not found');
                return;
            }
            
            // Check if this is a Ziina merchant
            if ($website->merchant_name !== 'ziina') {
                $this->_sendErrorResponse('Invalid merchant type');
                return;
            }
            
            // Process payment
            $this->_processPayment($order_data, $order_items, $website, $params, $params_get, $card_info);
            
        } catch (Exception $e) {
            $this->_logError('API Error: ' . $e->getMessage());
            $this->_sendErrorResponse('Payment processing failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Process Ziina payment
     */
    private function _processPayment($order_data, $order_items, $website, $params, $params_get, $card_info) {
        // Parse website configuration
        $json = json_decode($website->json);
        
        if (!$json) {
            throw new Exception('Invalid website configuration');
        }
        
        $api_key = $json->api_key;
        $secret_key = $json->secret_key;
        $sandbox = isset($json->sandbox) ? $json->sandbox : true;
        
        // Initialize Ziina API
        $this->ziina_api = new ZiinaAPI($api_key, $secret_key, $sandbox);
        
        // Validate order data
        if (!isset($order_data['total']) || !isset($order_data['currency'])) {
            throw new Exception('Invalid order data');
        }
        
        $amount = floatval($order_data['total']);
        $currency = strtoupper($order_data['currency']);
        
        // Validate amount and currency
        if (!$this->ziina_api->validateAmount($amount, $currency)) {
            throw new Exception('Invalid payment amount');
        }
        
        if (!ZiinaConfig::isValidCurrency($currency)) {
            throw new Exception('Unsupported currency: ' . $currency);
        }
        
        // Insert transaction record
        $transaction_data = array(
            'website_id' => $website->website_id,
            'order_number' => $order_data['number'],
            'amount' => $amount,
            'currency' => $currency,
            'status' => ZIINA_STATUS_PENDING,
            'created_at' => date('Y-m-d H:i:s'),
            'order_data' => serialize($order_data),
            'order_items' => serialize($order_items),
            'params' => $params_get
        );
        
        $this->db->insert('ziina_transactions', $transaction_data);
        $transaction_id = $this->db->insert_id();
        
        // Prepare payment intent options
        $payment_options = array(
            'description' => 'Order #' . $order_data['number'],
            'metadata' => array(
                'transaction_id' => $transaction_id,
                'order_number' => $order_data['number'],
                'website_id' => $website->website_id
            ),
            'return_url' => site_url('ziina/success/' . $transaction_id),
            'cancel_url' => site_url('ziina/cancel/' . $transaction_id)
        );
        
        if (isset($order_data['customer_email'])) {
            $payment_options['customer_email'] = $order_data['customer_email'];
        }
        
        // Format amount for API
        $formatted_amount = $this->ziina_api->formatAmount($amount, $currency);
        
        // Create payment intent
        $payment_intent = $this->ziina_api->createPaymentIntent(
            $formatted_amount, 
            $currency, 
            $payment_options
        );
        
        if (!$payment_intent || !isset($payment_intent['id'])) {
            throw new Exception('Failed to create payment intent');
        }
        
        // Update transaction with payment intent ID
        $this->db->where('id', $transaction_id);
        $this->db->update('ziina_transactions', array(
            'payment_intent_id' => $payment_intent['id'],
            'status' => ZIINA_STATUS_PROCESSING
        ));
        
        // Return payment response
        $this->_sendSuccessResponse(array(
            'payment_intent' => $payment_intent,
            'transaction_id' => $transaction_id,
            'redirect_url' => isset($payment_intent['next_action']['redirect_to_url']['url']) 
                ? $payment_intent['next_action']['redirect_to_url']['url'] 
                : null
        ));
    }
    
    /**
     * Handle successful payment
     */
    public function success($transaction_id = null) {
        if (!$transaction_id) {
            show_404();
            return;
        }
        
        try {
            // Get transaction
            $transaction = $this->db->where('id', $transaction_id)
                                  ->get('ziina_transactions')
                                  ->row();
            
            if (!$transaction) {
                show_404();
                return;
            }
            
            // Get payment intent ID from URL parameters
            $payment_intent_id = $this->input->get('payment_intent');
            
            if ($payment_intent_id && $payment_intent_id === $transaction->payment_intent_id) {
                // Verify payment with Ziina API
                $this->_verifyPayment($transaction);
            }
            
            // Load success view
            $data = array(
                'transaction' => $transaction,
                'success' => true
            );
            
            $this->load->view('ziina/payment_result', $data);
            
        } catch (Exception $e) {
            $this->_logError('Success handler error: ' . $e->getMessage());
            $this->load->view('ziina/payment_result', array('success' => false, 'error' => $e->getMessage()));
        }
    }
    
    /**
     * Handle cancelled payment
     */
    public function cancel($transaction_id = null) {
        if (!$transaction_id) {
            show_404();
            return;
        }
        
        // Update transaction status
        $this->db->where('id', $transaction_id);
        $this->db->update('ziina_transactions', array(
            'status' => ZIINA_STATUS_CANCELED,
            'updated_at' => date('Y-m-d H:i:s')
        ));
        
        // Load cancel view
        $transaction = $this->db->where('id', $transaction_id)
                              ->get('ziina_transactions')
                              ->row();
        
        $data = array(
            'transaction' => $transaction,
            'cancelled' => true
        );
        
        $this->load->view('ziina/payment_result', $data);
    }
    
    /**
     * Verify payment status
     */
    private function _verifyPayment($transaction) {
        // Get website configuration
        $website = $this->db->where('website_id', $transaction->website_id)
                           ->join('merchant', 'merchant.merchant_id = websites.merchant_id')
                           ->get('websites')
                           ->row();
        
        if (!$website) {
            throw new Exception('Website configuration not found');
        }
        
        $json = json_decode($website->json);
        $this->ziina_api = new ZiinaAPI($json->api_key, $json->secret_key, $json->sandbox);
        
        // Retrieve payment intent from Ziina
        $payment_intent = $this->ziina_api->retrievePaymentIntent($transaction->payment_intent_id);
        
        if ($payment_intent && isset($payment_intent['status'])) {
            // Update transaction status
            $this->db->where('id', $transaction->id);
            $this->db->update('ziina_transactions', array(
                'status' => $payment_intent['status'],
                'updated_at' => date('Y-m-d H:i:s'),
                'payment_response' => json_encode($payment_intent)
            ));
        }
    }
    
    /**
     * Send success response
     */
    private function _sendSuccessResponse($data) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => true,
            'data' => $data
        ));
        exit;
    }
    
    /**
     * Send error response
     */
    private function _sendErrorResponse($message) {
        header('Content-Type: application/json');
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'error' => $message
        ));
        exit;
    }
    
    /**
     * Log error
     */
    private function _logError($message) {
        $log_file = APPPATH . 'logs/ziina_errors.log';
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
}
