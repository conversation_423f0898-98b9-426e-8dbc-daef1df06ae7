<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once('application/third_party/authorize-net/autoload.php');
use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;

class Fpaypcc extends CI_Controller {

	public function api()
	{
	   //file_put_contents('rashid.txt',$this->input->post('params'));exit();
	   
	   	$params=$this->input->post('params');
		//$params=file_get_contents('rashid.txt');
	   
	   	$params = str_replace(' ','+',$params);
		$params_get = $params;

		$params = base64_decode($params);
		$params = unserialize($params);
		
		$order_data = unserialize($params['order_data']);
		$order_items = unserialize($params['order_items']);
		$card_info = unserialize($params['card_info']);

		$parse_client_url=parse_url($params['client_url']);

		$website=$this->db->query("SELECT * FROM `websites` INNER JOIN merchant on merchant.merchant_id = websites.merchant_id where websites.url=?",array($parse_client_url['host']))->row();
			
		if(isset($website))
		{
			if($website->merchant_name=='authorizenet')
			{
				$json = json_decode($website->json);

				$API_LOGIN_ID= $json->API_LOGIN_ID;
				$TRANSACTION_KEY= $json->TRANSACTION_KEY;
				$dname = $json->dname;
				$sandBox = false;

				if($website->name=='Authorize-net Test')
				{
					$sandBox = true;
				}

				$this->db->where(array(
					'website_id'     => $website->website_id,
					'number'       => $order_data['number'],
				));

				$query=$this->db->get('transations');

				$insert_id=0;

				if($query->num_rows()==0)
				{
					$insert_data=array(
						'status'         => 'pending',
						'user_id'        => $website->user_id,
						'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
						'client_email'   => $order_data['billing']['email'],
						'currency'       => strtoupper($order_data['currency']),
						'amount'         => $order_data['total'] ,
						'order_id'       => $order_data['id'],
						'number'         => $order_data['number'],
						'client_url'     => $params['client_url'],
						'merchant_id'    => $website->merchant_id,
						'merchant_name'  => $website->merchant_name,
						'merchant_title' => $website->name,
						'section_id'     => '',
						'website_id'     => $website->website_id,
						'created_date'   => date('Y-m-d H:i:s')
					);
			
					$this->db->insert('transations',$insert_data);
					$insert_id = $this->db->insert_id();
				}
				else
				{
					$row = $query->row();
					$insert_id=$row->id;

					if($row->status=='succeeded')
					{
						$send_data = array(
							'merchant' => 'fpay/Authorize-net',
							'trans_id' => $row->id,
							'client_url' => $row->client_url,
							'order_id' => $row->order_id,
							'payment_status' => $row->status
						);
				
						//redirect($row->client_url.'/wc-api/fpay_callback?params='.base64_encode(serialize($send_data)));
						exit();
					}

					$insert_data=array(
						'status'         => 'pending',
						'user_id'        => $website->user_id,
						'client_name'    => $order_data['billing']['first_name'].' '.$order_data['billing']['last_name'],
						'client_email'   => $order_data['billing']['email'],
						'currency'       => strtoupper($order_data['currency']),
						'amount'         => $order_data['total'] ,
						'order_id'       => $order_data['id'],
						'number'         => $order_data['number'],
						'client_url'     => $params['client_url'],
						'merchant_id'    => $website->merchant_id,
						'merchant_name'  => $website->merchant_name,
						'merchant_title' => $website->name,
						'section_id'     => '',
						'website_id'     => $website->website_id,
						'created_date'   => date('Y-m-d H:i:s')
					);
					$this->db->where(array(
						'website_id'     => $website->website_id,
						'order_id'       => $order_data['id'],
					));
					$this->db->update('transations',$insert_data);
				}

				

				file_put_contents('logs/transations/'.$insert_id.'.txt',$params_get);


				$item_data = array(
					'dname' => $dname,
					'client_url' => $params['client_url'],
					'item_name' => 'Order #'.$website->website_id.'-'.$order_data['id'],
					'item_number' => $insert_id,
					'amount' => $order_data['total'],
					'currency_code' => $order_data['currency']
				);

				$transations_id = $insert_id;
				$row=$this->db->query("SELECT * FROM `transations` where id=?",array($transations_id))->row();


				$cardNumber=str_replace(' ','',$card_info['fpaypcc-card-number']);
				$expirationDate=str_replace(' ','',$card_info['fpaypcc-card-expiry']);
				$cardDate = explode('/',$expirationDate);
				$securityCode=$card_info['fpaypcc-card-cvc'];
				$order_number = $transations_id;
				$amount = $item_data['amount'];

				$cardDetails=array(
					'card-number'=>$cardNumber,
					'year'=>"20".$cardDate[1],
					'month'=>$cardDate[0],
					'card-code'=>$securityCode,
					'amount'=>$amount
				);
				
				$refId = 'ref' . $order_number."_44";

				$ANetEnvironment = "https://api2.authorize.net";

				if($sandBox)
				{
					$ANetEnvironment = "https://apitest.authorize.net";
				}

				$merchantAuthentication = new AnetAPI\MerchantAuthenticationType();
				$merchantAuthentication->setName($API_LOGIN_ID);
				$merchantAuthentication->setTransactionKey($TRANSACTION_KEY);  

				$creditCard = new AnetAPI\CreditCardType();
				$creditCard->setCardNumber($cardDetails["card-number"]);
				$creditCard->setExpirationDate( $cardDetails["year"] . "-" . $cardDetails["month"]);
				$creditCard->setCardCode($cardDetails["card-code"]);
				$paymentType = new AnetAPI\PaymentType();
				$paymentType->setCreditCard($creditCard);

				$orderType = new AnetAPI\OrderType();
				$orderType->setInvoiceNumber( "FP".$transations_id.'-'.$order_data['number'] );
				$orderType->setDescription( "Customized Apparel" );

				$customerDataOne = new AnetAPI\CustomerDataType();
				$customerDataOne->setEmail( $order_data['billing']['email'] );

				$customerAddressOne = new AnetAPI\CustomerAddressType();
				$customerAddressOne->setFirstName( $order_data['billing']['first_name'] );
				$customerAddressOne->setLastName( $order_data['billing']['last_name'] );
				$customerAddressOne->setCompany( $order_data['billing']['company'] );
				$customerAddressOne->setAddress( $order_data['billing']['address_1'] );
				$customerAddressOne->setCity( $order_data['billing']['city'] );
				$customerAddressOne->setState( $order_data['billing']['state'] );
				$customerAddressOne->setZip( $order_data['billing']['postcode'] );
				$customerAddressOne->setCountry( $order_data['billing']['country'] );
				$customerAddressOne->setPhoneNumber( $order_data['billing']['phone'] );

				$customerAddressShip = new AnetAPI\NameAndAddressType();
				$customerAddressShip->setFirstName( $order_data['billing']['first_name'] );
				$customerAddressShip->setLastName( $order_data['billing']['last_name'] );
				$customerAddressShip->setCompany( $order_data['billing']['company'] );
				$customerAddressShip->setAddress( $order_data['billing']['address_1'] );
				$customerAddressShip->setCity( $order_data['billing']['city'] );
				$customerAddressShip->setState( $order_data['billing']['state'] );
				$customerAddressShip->setZip( $order_data['billing']['postcode'] );
				$customerAddressShip->setCountry( $order_data['billing']['country'] );

				$transactionRequestType = new AnetAPI\TransactionRequestType();
				$transactionRequestType->setTransactionType("authCaptureTransaction");
				$transactionRequestType->setAmount($cardDetails['amount']);
				$transactionRequestType->setPayment($paymentType);
				$transactionRequestType->setOrder( $orderType);
				$transactionRequestType->setCustomer( $customerDataOne);
				$transactionRequestType->setBillTo( $customerAddressOne);
				$transactionRequestType->setShipTo( $customerAddressShip);

				$request = new AnetAPI\CreateTransactionRequest();
				$request->setMerchantAuthentication($merchantAuthentication);
				$request->setRefId( $refId);
				$request->setTransactionRequest($transactionRequestType);
				$controller = new AnetController\CreateTransactionController($request);
				$response = $controller->executeWithApiResponse($ANetEnvironment);

				if ( null != $response)
				{
					$tresponse = $response->getTransactionResponse();
					
					if (($tresponse != null) && ($tresponse->getResponseCode()=="1"))
					{
						$authCode = $tresponse->getAuthCode();
						$paymentResponse = $tresponse->getMessages()[0]->getDescription();
						$transId=$tresponse->getTransId();

						$this->db->where('id',$row->id);
						$this->db->update('transations',array('status'=>'succeeded','transection_id'=>$transId));

						$send_data = array(
							'merchant' => 'fpay/Authorize-net',
							'trans_id' => $row->id,
							'client_url' => $row->client_url,
							'order_id' => $row->order_id,
							'payment_status' => 'succeeded'
						);

						$redirect_url = $row->client_url.'/wc-api/fpay_callback?params='.base64_encode(serialize($send_data));

						echo json_encode(array(
							'status'=>"succeeded",
							'transaction_id' => $transations_id,
							'auth_code' => 'fpay/Authorize-net',
							'merchant' => 'fpay/Authorize-net'
						));
					}
					else
					{
						$paymentResponse = $tresponse->getErrors()[0]->getErrorText();
						echo json_encode(array(
							'message'=>$paymentResponse,
							'status' => false
						));
						
					}
				}





































			}
		}
	    
	}
}