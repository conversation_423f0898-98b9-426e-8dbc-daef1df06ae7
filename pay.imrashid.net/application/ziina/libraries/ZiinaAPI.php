<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Ziina API Library
 * 
 * Handles all API communication with Ziina payment gateway
 * 
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

require_once(APPPATH . 'ziina/config/ziina_config.php');

class ZiinaAPI {
    
    private $api_key;
    private $secret_key;
    private $sandbox;
    private $base_url;
    private $CI;
    
    /**
     * Constructor
     */
    public function __construct($api_key = null, $secret_key = null, $sandbox = true) {
        $this->CI =& get_instance();
        $this->api_key = $api_key;
        $this->secret_key = $secret_key;
        $this->sandbox = $sandbox;
        $this->base_url = ZiinaConfig::getApiUrl($sandbox);
        
        // Load required helpers
        $this->CI->load->helper('url');
        $this->CI->load->library('session');
    }
    
    /**
     * Set API credentials
     */
    public function setCredentials($api_key, $secret_key) {
        $this->api_key = $api_key;
        $this->secret_key = $secret_key;
    }
    
    /**
     * Set sandbox mode
     */
    public function setSandbox($sandbox = true) {
        $this->sandbox = $sandbox;
        $this->base_url = ZiinaConfig::getApiUrl($sandbox);
    }
    
    /**
     * Make API request
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->base_url . $endpoint;
        
        $headers = array(
            'Authorization: Bearer ' . $this->api_key,
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: ' . ZiinaConfig::get('user_agent')
        );
        
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => ZiinaConfig::get('timeout'),
            CURLOPT_CONNECTTIMEOUT => ZiinaConfig::get('connect_timeout'),
            CURLOPT_SSL_VERIFYPEER => ZiinaConfig::get('verify_ssl'),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_CUSTOMREQUEST => $method
        ));
        
        if ($data && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        $decoded_response = json_decode($response, true);
        
        if ($http_code >= 400) {
            $error_message = isset($decoded_response['error']['message']) 
                ? $decoded_response['error']['message'] 
                : 'HTTP Error ' . $http_code;
            throw new Exception($error_message, $http_code);
        }
        
        return $decoded_response;
    }
    
    /**
     * Create payment intent
     */
    public function createPaymentIntent($amount, $currency, $options = array()) {
        $data = array(
            'amount' => $amount,
            'currency' => strtoupper($currency),
            'payment_method_types' => isset($options['payment_method_types']) 
                ? $options['payment_method_types'] 
                : array(ZIINA_PAYMENT_METHOD_CARD)
        );
        
        // Add optional parameters
        if (isset($options['description'])) {
            $data['description'] = $options['description'];
        }
        
        if (isset($options['metadata'])) {
            $data['metadata'] = $options['metadata'];
        }
        
        if (isset($options['customer_email'])) {
            $data['customer_email'] = $options['customer_email'];
        }
        
        if (isset($options['return_url'])) {
            $data['return_url'] = $options['return_url'];
        }
        
        if (isset($options['cancel_url'])) {
            $data['cancel_url'] = $options['cancel_url'];
        }
        
        return $this->makeRequest(ZIINA_PAYMENT_INTENT_ENDPOINT, 'POST', $data);
    }
    
    /**
     * Retrieve payment intent
     */
    public function retrievePaymentIntent($payment_intent_id) {
        $endpoint = str_replace('{id}', $payment_intent_id, ZIINA_PAYMENT_RETRIEVE_ENDPOINT);
        return $this->makeRequest($endpoint, 'GET');
    }
    
    /**
     * Confirm payment intent
     */
    public function confirmPaymentIntent($payment_intent_id, $payment_method = null) {
        $endpoint = str_replace('{id}', $payment_intent_id, ZIINA_PAYMENT_CONFIRM_ENDPOINT);
        
        $data = array();
        if ($payment_method) {
            $data['payment_method'] = $payment_method;
        }
        
        return $this->makeRequest($endpoint, 'POST', $data);
    }
    
    /**
     * Cancel payment intent
     */
    public function cancelPaymentIntent($payment_intent_id) {
        $endpoint = str_replace('{id}', $payment_intent_id, ZIINA_PAYMENT_CANCEL_ENDPOINT);
        return $this->makeRequest($endpoint, 'POST');
    }
    
    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature($payload, $signature, $webhook_secret) {
        $expected_signature = hash_hmac('sha256', $payload, $webhook_secret);
        return hash_equals($expected_signature, $signature);
    }
    
    /**
     * Log API activity
     */
    private function logActivity($message, $level = 'info') {
        $log_file = APPPATH . 'logs/' . ZiinaConfig::get('log_file');
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies() {
        return ZiinaConfig::get('supported_currencies');
    }
    
    /**
     * Get supported payment methods
     */
    public function getSupportedPaymentMethods() {
        return ZiinaConfig::get('supported_payment_methods');
    }
    
    /**
     * Validate amount
     */
    public function validateAmount($amount, $currency) {
        if (!is_numeric($amount) || $amount <= 0) {
            return false;
        }
        
        // Convert to smallest currency unit (fils for AED)
        if ($currency === ZIINA_CURRENCY_AED) {
            return $amount >= 1; // Minimum 1 fils
        }
        
        return $amount >= 0.01; // Minimum for other currencies
    }
    
    /**
     * Format amount for API
     */
    public function formatAmount($amount, $currency) {
        if ($currency === ZIINA_CURRENCY_AED) {
            return intval($amount * 100); // Convert to fils
        }
        
        return intval($amount * 100); // Convert to cents
    }
}
