<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Ziina Payment Gateway Configuration
 * 
 * Configuration settings for Ziina payment integration
 * 
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

// Ziina API Configuration
define('ZIINA_SANDBOX_URL', 'https://api.sandbox.ziina.com');
define('ZIINA_PRODUCTION_URL', 'https://api.ziina.com');

// Ziina API Endpoints
define('ZIINA_PAYMENT_INTENT_ENDPOINT', '/v1/payment-intents');
define('ZIINA_PAYMENT_CONFIRM_ENDPOINT', '/v1/payment-intents/{id}/confirm');
define('ZIINA_PAYMENT_CANCEL_ENDPOINT', '/v1/payment-intents/{id}/cancel');
define('ZIINA_PAYMENT_RETRIEVE_ENDPOINT', '/v1/payment-intents/{id}');
define('ZIINA_WEBHOOK_ENDPOINT', '/v1/webhooks');

// Ziina Payment Methods
define('ZIINA_PAYMENT_METHOD_CARD', 'card');
define('ZIINA_PAYMENT_METHOD_WALLET', 'wallet');
define('ZIINA_PAYMENT_METHOD_BANK_TRANSFER', 'bank_transfer');

// Ziina Payment Status
define('ZIINA_STATUS_PENDING', 'pending');
define('ZIINA_STATUS_PROCESSING', 'processing');
define('ZIINA_STATUS_SUCCEEDED', 'succeeded');
define('ZIINA_STATUS_FAILED', 'failed');
define('ZIINA_STATUS_CANCELED', 'canceled');
define('ZIINA_STATUS_REQUIRES_ACTION', 'requires_action');

// Ziina Currency Codes (UAE Dirham)
define('ZIINA_CURRENCY_AED', 'AED');
define('ZIINA_CURRENCY_USD', 'USD');
define('ZIINA_CURRENCY_EUR', 'EUR');

// Ziina Configuration Class
class ZiinaConfig {
    
    /**
     * Default configuration settings
     */
    public static $config = array(
        'api_version' => 'v1',
        'timeout' => 30,
        'connect_timeout' => 10,
        'max_retries' => 3,
        'retry_delay' => 1000, // milliseconds
        'verify_ssl' => true,
        'user_agent' => 'Ziina-PHP/1.0.0',
        'default_currency' => ZIINA_CURRENCY_AED,
        'supported_currencies' => array(
            ZIINA_CURRENCY_AED,
            ZIINA_CURRENCY_USD,
            ZIINA_CURRENCY_EUR
        ),
        'supported_payment_methods' => array(
            ZIINA_PAYMENT_METHOD_CARD,
            ZIINA_PAYMENT_METHOD_WALLET,
            ZIINA_PAYMENT_METHOD_BANK_TRANSFER
        ),
        'webhook_tolerance' => 300, // 5 minutes
        'log_level' => 'info', // debug, info, warning, error
        'log_file' => 'ziina_payments.log'
    );
    
    /**
     * Get configuration value
     */
    public static function get($key, $default = null) {
        return isset(self::$config[$key]) ? self::$config[$key] : $default;
    }
    
    /**
     * Set configuration value
     */
    public static function set($key, $value) {
        self::$config[$key] = $value;
    }
    
    /**
     * Get API URL based on environment
     */
    public static function getApiUrl($sandbox = false) {
        return $sandbox ? ZIINA_SANDBOX_URL : ZIINA_PRODUCTION_URL;
    }
    
    /**
     * Validate currency code
     */
    public static function isValidCurrency($currency) {
        return in_array(strtoupper($currency), self::$config['supported_currencies']);
    }
    
    /**
     * Validate payment method
     */
    public static function isValidPaymentMethod($method) {
        return in_array($method, self::$config['supported_payment_methods']);
    }
}

// Error codes and messages
class ZiinaErrorCodes {
    
    const INVALID_API_KEY = 'invalid_api_key';
    const INVALID_AMOUNT = 'invalid_amount';
    const INVALID_CURRENCY = 'invalid_currency';
    const INVALID_PAYMENT_METHOD = 'invalid_payment_method';
    const PAYMENT_FAILED = 'payment_failed';
    const PAYMENT_DECLINED = 'payment_declined';
    const INSUFFICIENT_FUNDS = 'insufficient_funds';
    const CARD_EXPIRED = 'card_expired';
    const INVALID_CARD = 'invalid_card';
    const NETWORK_ERROR = 'network_error';
    const WEBHOOK_VERIFICATION_FAILED = 'webhook_verification_failed';
    
    public static $messages = array(
        self::INVALID_API_KEY => 'Invalid API key provided',
        self::INVALID_AMOUNT => 'Invalid payment amount',
        self::INVALID_CURRENCY => 'Invalid or unsupported currency',
        self::INVALID_PAYMENT_METHOD => 'Invalid payment method',
        self::PAYMENT_FAILED => 'Payment processing failed',
        self::PAYMENT_DECLINED => 'Payment was declined',
        self::INSUFFICIENT_FUNDS => 'Insufficient funds',
        self::CARD_EXPIRED => 'Card has expired',
        self::INVALID_CARD => 'Invalid card details',
        self::NETWORK_ERROR => 'Network connection error',
        self::WEBHOOK_VERIFICATION_FAILED => 'Webhook signature verification failed'
    );
    
    public static function getMessage($code) {
        return isset(self::$messages[$code]) ? self::$messages[$code] : 'Unknown error';
    }
}
