<?php
/**
 * Ziina Payment Callback Handler
 *
 * Handles payment notifications and status updates from <PERSON><PERSON><PERSON>
 *
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Configuration
define('ZIINA_SANDBOX_URL', 'https://api.sandbox.ziina.com');
define('ZIINA_PRODUCTION_URL', 'https://api.ziina.com');
define('ZIINA_SANDBOX_MODE', true); // Set to false for production

// Your Ziina API credentials (replace with your actual credentials)
define('ZIINA_API_KEY', 'your_ziina_api_key_here');
define('ZIINA_SECRET_KEY', 'your_ziina_secret_key_here');
define('ZIINA_WEBHOOK_SECRET', 'your_webhook_secret_here'); // For webhook signature verification

/**
 * Ziina API Class (simplified version for callback)
 */
class ZiinaCallbackAPI {
    private $api_key;
    private $base_url;

    public function __construct($api_key, $sandbox = true) {
        $this->api_key = $api_key;
        $this->base_url = $sandbox ? ZIINA_SANDBOX_URL : ZIINA_PRODUCTION_URL;
    }

    /**
     * Retrieve payment intent
     */
    public function retrievePaymentIntent($payment_intent_id) {
        $url = $this->base_url . '/v1/payment-intents/' . $payment_intent_id;

        $headers = array(
            'Authorization: Bearer ' . $this->api_key,
            'Content-Type: application/json',
            'Accept: application/json'
        );

        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }

        if ($http_code >= 400) {
            throw new Exception('HTTP Error ' . $http_code);
        }

        return json_decode($response, true);
    }
}

/**
 * Helper Functions
 */
function logCallback($message) {
    $log_file = __DIR__ . '/callback_log.txt';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

function verifyWebhookSignature($payload, $signature, $secret) {
    $expected_signature = hash_hmac('sha256', $payload, $secret);
    return hash_equals($expected_signature, $signature);
}

function sendResponse($success, $message, $data = null) {
    header('Content-Type: application/json');
    http_response_code($success ? 200 : 400);

    $response = array(
        'success' => $success,
        'message' => $message
    );

    if ($data) {
        $response['data'] = $data;
    }

    echo json_encode($response);
    exit;
}

// Main callback processing
try {
    // Log the callback request
    logCallback("Callback received: " . $_SERVER['REQUEST_METHOD'] . " " . $_SERVER['REQUEST_URI']);

    // Handle different types of callbacks
    $method = $_SERVER['REQUEST_METHOD'];

    if ($method === 'POST') {
        // Handle webhook (POST request from Ziina)
        handleWebhook();
    } else {
        // Handle redirect callback (GET request from user's browser)
        handleRedirectCallback();
    }

} catch (Exception $e) {
    logCallback("Callback error: " . $e->getMessage());
    sendResponse(false, 'Callback processing failed: ' . $e->getMessage());
}

/**
 * Handle webhook notifications from Ziina
 */
function handleWebhook() {
    // Get the raw POST data
    $payload = file_get_contents('php://input');

    if (empty($payload)) {
        throw new Exception('Empty webhook payload');
    }

    // Get the signature header
    $signature = isset($_SERVER['HTTP_ZIINA_SIGNATURE']) ? $_SERVER['HTTP_ZIINA_SIGNATURE'] : '';

    // Verify webhook signature (if webhook secret is configured)
    if (ZIINA_WEBHOOK_SECRET && !verifyWebhookSignature($payload, $signature, ZIINA_WEBHOOK_SECRET)) {
        logCallback("Webhook signature verification failed");
        throw new Exception('Invalid webhook signature');
    }

    // Parse the webhook data
    $webhook_data = json_decode($payload, true);

    if (!$webhook_data) {
        throw new Exception('Invalid webhook JSON');
    }

    logCallback("Webhook received: " . json_encode($webhook_data));

    // Process the webhook based on event type
    $event_type = isset($webhook_data['type']) ? $webhook_data['type'] : 'unknown';
    $payment_intent = isset($webhook_data['data']['object']) ? $webhook_data['data']['object'] : null;

    switch ($event_type) {
        case 'payment_intent.succeeded':
            handlePaymentSuccess($payment_intent);
            break;

        case 'payment_intent.payment_failed':
            handlePaymentFailure($payment_intent);
            break;

        case 'payment_intent.canceled':
            handlePaymentCancellation($payment_intent);
            break;

        default:
            logCallback("Unhandled webhook event type: " . $event_type);
            break;
    }

    sendResponse(true, 'Webhook processed successfully');
}