<?php

$payment_id = $_GET['payment_id'] ?? null;

// Optionally verify payment status
// Example (if API supports it):
$ch = curl_init("$base_url/v1/payments/$payment_id");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: Bearer $api_key",
]);
$response = curl_exec($ch);
curl_close($ch);

$result = json_decode($response, true);

if ($result['status'] == 'success') {
    echo 'Mark order as paid';
} else {
    echo 'Payment failed';
}