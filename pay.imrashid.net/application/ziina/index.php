<?php
/**
 * Ziina Payment Gateway Integration - Complete Solution
 *
 * A complete Ziina payment integration in a single PHP file
 *
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Configuration
define('ZIINA_SANDBOX_URL', 'https://api.sandbox.ziina.com');
define('ZIINA_PRODUCTION_URL', 'https://api.ziina.com');
define('ZIINA_SANDBOX_MODE', true); // Set to false for production

// Your Ziina API credentials (replace with your actual credentials)
define('ZIINA_API_KEY', 'your_ziina_api_key_here');
define('ZIINA_SECRET_KEY', 'your_ziina_secret_key_here');

// Payment status constants
define('ZIINA_STATUS_PENDING', 'pending');
define('ZIINA_STATUS_PROCESSING', 'processing');
define('ZIINA_STATUS_SUCCEEDED', 'succeeded');
define('ZIINA_STATUS_FAILED', 'failed');
define('ZIINA_STATUS_CANCELED', 'canceled');

// Currency constants
define('ZIINA_CURRENCY_AED', 'AED');
define('ZIINA_CURRENCY_USD', 'USD');

/**
 * Ziina API Class
 */
class ZiinaAPI {
    private $api_key;
    private $secret_key;
    private $sandbox;
    private $base_url;

    public function __construct($api_key, $secret_key, $sandbox = true) {
        $this->api_key = $api_key;
        $this->secret_key = $secret_key;
        $this->sandbox = $sandbox;
        $this->base_url = $sandbox ? ZIINA_SANDBOX_URL : ZIINA_PRODUCTION_URL;
    }

    /**
     * Make API request to Ziina
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->base_url . $endpoint;

        $headers = array(
            'Authorization: Bearer ' . $this->api_key,
            'Content-Type: application/json',
            'Accept: application/json'
        );

        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_SSL_VERIFYPEER => false
        ));

        if ($data && in_array($method, array('POST', 'PUT'))) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }

        $decoded_response = json_decode($response, true);

        if ($http_code >= 400) {
            $error_message = isset($decoded_response['error']['message'])
                ? $decoded_response['error']['message']
                : 'HTTP Error ' . $http_code;
            throw new Exception($error_message, $http_code);
        }

        return $decoded_response;
    }

    /**
     * Create payment intent
     */
    public function createPaymentIntent($amount, $currency, $options = array()) {
        $data = array(
            'amount' => intval($amount * 100), // Convert to smallest currency unit (fils for AED)
            'currency' => strtoupper($currency),
            'payment_method_types' => array('card')
        );

        if (isset($options['description'])) {
            $data['description'] = $options['description'];
        }

        if (isset($options['customer_email'])) {
            $data['customer_email'] = $options['customer_email'];
        }

        if (isset($options['customer_name'])) {
            $data['customer_name'] = $options['customer_name'];
        }

        if (isset($options['return_url'])) {
            $data['return_url'] = $options['return_url'];
        }

        if (isset($options['cancel_url'])) {
            $data['cancel_url'] = $options['cancel_url'];
        }

        if (isset($options['metadata'])) {
            $data['metadata'] = $options['metadata'];
        }

        return $this->makeRequest('/v1/payment-intents', 'POST', $data);
    }

    /**
     * Retrieve payment intent
     */
    public function retrievePaymentIntent($payment_intent_id) {
        return $this->makeRequest('/v1/payment-intents/' . $payment_intent_id, 'GET');
    }

    /**
     * Confirm payment intent
     */
    public function confirmPaymentIntent($payment_intent_id) {
        return $this->makeRequest('/v1/payment-intents/' . $payment_intent_id . '/confirm', 'POST');
    }

    /**
     * Cancel payment intent
     */
    public function cancelPaymentIntent($payment_intent_id) {
        return $this->makeRequest('/v1/payment-intents/' . $payment_intent_id . '/cancel', 'POST');
    }
}

/**
 * Helper Functions
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    return $protocol . '://' . $host . $script;
}

function generateTransactionId() {
    return 'ZN_' . time() . '_' . mt_rand(1000, 9999);
}

function logMessage($message) {
    $log_file = __DIR__ . '/ziina_log.txt';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

function validateAmount($amount) {
    return is_numeric($amount) && $amount > 0;
}

function formatCurrency($amount, $currency = 'AED') {
    $symbols = array(
        'AED' => 'د.إ',
        'USD' => '$',
        'EUR' => '€'
    );
    $symbol = isset($symbols[$currency]) ? $symbols[$currency] : $currency;
    return $symbol . ' ' . number_format($amount, 2);
}

/**
 * Payment Form Function
 */
function showPaymentForm() {
    $error = isset($_SESSION['error']) ? $_SESSION['error'] : '';
    unset($_SESSION['error']);
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ziina Payment Gateway</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
            button { background: #007cba; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #005a87; }
            .error { color: red; margin-bottom: 15px; }
            .card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9; }
        </style>
    </head>
    <body>
        <div class="card">
            <h2>Ziina Payment Gateway</h2>

            <?php if ($error): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <form method="POST" action="?action=process">
                <div class="form-group">
                    <label for="amount">Amount:</label>
                    <input type="number" id="amount" name="amount" step="0.01" min="1" required value="100.00">
                </div>

                <div class="form-group">
                    <label for="currency">Currency:</label>
                    <select id="currency" name="currency" required>
                        <option value="AED">AED - UAE Dirham</option>
                        <option value="USD">USD - US Dollar</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="description">Description:</label>
                    <input type="text" id="description" name="description" required value="Test Payment">
                </div>

                <div class="form-group">
                    <label for="customer_name">Customer Name:</label>
                    <input type="text" id="customer_name" name="customer_name" required value="John Doe">
                </div>

                <div class="form-group">
                    <label for="customer_email">Customer Email:</label>
                    <input type="email" id="customer_email" name="customer_email" required value="<EMAIL>">
                </div>

                <button type="submit">Pay with Ziina</button>
            </form>
        </div>
    </body>
    </html>
    <?php
}

/**
 * Process Payment Function
 */
function processPayment() {
    try {
        // Validate input
        $amount = floatval($_POST['amount']);
        $currency = strtoupper($_POST['currency']);
        $description = $_POST['description'];
        $customer_name = $_POST['customer_name'];
        $customer_email = $_POST['customer_email'];

        if (!validateAmount($amount)) {
            throw new Exception('Invalid amount');
        }

        if (!in_array($currency, array('AED', 'USD'))) {
            throw new Exception('Invalid currency');
        }

        // Generate transaction ID
        $transaction_id = generateTransactionId();

        // Store transaction data in session
        $_SESSION['transaction'] = array(
            'id' => $transaction_id,
            'amount' => $amount,
            'currency' => $currency,
            'description' => $description,
            'customer_name' => $customer_name,
            'customer_email' => $customer_email,
            'created_at' => date('Y-m-d H:i:s')
        );

        // Initialize Ziina API
        $ziina = new ZiinaAPI(ZIINA_API_KEY, ZIINA_SECRET_KEY, ZIINA_SANDBOX_MODE);

        // Prepare payment options
        $current_url = getCurrentUrl();
        $options = array(
            'description' => $description,
            'customer_name' => $customer_name,
            'customer_email' => $customer_email,
            'return_url' => $current_url . '?action=success&transaction_id=' . $transaction_id,
            'cancel_url' => $current_url . '?action=cancel&transaction_id=' . $transaction_id,
            'metadata' => array(
                'transaction_id' => $transaction_id,
                'customer_name' => $customer_name
            )
        );

        // Create payment intent
        $payment_intent = $ziina->createPaymentIntent($amount, $currency, $options);

        if (!$payment_intent || !isset($payment_intent['id'])) {
            throw new Exception('Failed to create payment intent');
        }

        // Store payment intent ID
        $_SESSION['transaction']['payment_intent_id'] = $payment_intent['id'];

        logMessage("Payment intent created: " . $payment_intent['id'] . " for transaction: " . $transaction_id);

        // Check if there's a redirect URL
        if (isset($payment_intent['next_action']['redirect_to_url']['url'])) {
            header('Location: ' . $payment_intent['next_action']['redirect_to_url']['url']);
            exit;
        } elseif (isset($payment_intent['payment_url'])) {
            header('Location: ' . $payment_intent['payment_url']);
            exit;
        } else {
            // Show payment form or confirmation
            showPaymentConfirmation($payment_intent);
        }

    } catch (Exception $e) {
        logMessage("Payment processing error: " . $e->getMessage());
        $_SESSION['error'] = 'Payment processing failed: ' . $e->getMessage();
        header('Location: ?action=form');
        exit;
    }
}

/**
 * Show Payment Confirmation
 */
function showPaymentConfirmation($payment_intent) {
    $transaction = $_SESSION['transaction'];
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Confirmation - Ziina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9; }
            .success { color: #28a745; }
            .info { background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 15px 0; }
        </style>
    </head>
    <body>
        <div class="card">
            <h2>Payment Confirmation</h2>
            <div class="info">
                <p><strong>Transaction ID:</strong> <?php echo htmlspecialchars($transaction['id']); ?></p>
                <p><strong>Amount:</strong> <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?></p>
                <p><strong>Description:</strong> <?php echo htmlspecialchars($transaction['description']); ?></p>
                <p><strong>Payment Intent ID:</strong> <?php echo htmlspecialchars($payment_intent['id']); ?></p>
                <p><strong>Status:</strong> <?php echo htmlspecialchars($payment_intent['status']); ?></p>
            </div>

            <?php if (isset($payment_intent['client_secret'])): ?>
                <p>Use the client secret to complete payment: <code><?php echo htmlspecialchars($payment_intent['client_secret']); ?></code></p>
            <?php endif; ?>

            <p><a href="?action=form">← Back to Payment Form</a></p>
        </div>
    </body>
    </html>
    <?php
}

/**
 * Show Success Page
 */
function showSuccess() {
    $transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';
    $payment_intent_id = isset($_GET['payment_intent']) ? $_GET['payment_intent'] : '';

    $transaction = isset($_SESSION['transaction']) ? $_SESSION['transaction'] : null;

    // Verify payment if payment_intent_id is provided
    $payment_status = 'unknown';
    if ($payment_intent_id && $transaction) {
        try {
            $ziina = new ZiinaAPI(ZIINA_API_KEY, ZIINA_SECRET_KEY, ZIINA_SANDBOX_MODE);
            $payment_intent = $ziina->retrievePaymentIntent($payment_intent_id);
            $payment_status = $payment_intent['status'];

            logMessage("Payment verified: " . $payment_intent_id . " Status: " . $payment_status);
        } catch (Exception $e) {
            logMessage("Payment verification error: " . $e->getMessage());
        }
    }

    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Success - Ziina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9; }
            .success { color: #28a745; text-align: center; }
            .info { background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
            .status.succeeded { background: #d4edda; color: #155724; }
            .status.pending { background: #fff3cd; color: #856404; }
            .status.failed { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <div class="card">
            <div class="success">
                <h2>✓ Payment Processed</h2>
            </div>

            <?php if ($transaction): ?>
                <div class="info">
                    <p><strong>Transaction ID:</strong> <?php echo htmlspecialchars($transaction['id']); ?></p>
                    <p><strong>Amount:</strong> <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?></p>
                    <p><strong>Description:</strong> <?php echo htmlspecialchars($transaction['description']); ?></p>
                    <p><strong>Customer:</strong> <?php echo htmlspecialchars($transaction['customer_name']); ?></p>
                    <?php if ($payment_intent_id): ?>
                        <p><strong>Payment Intent ID:</strong> <?php echo htmlspecialchars($payment_intent_id); ?></p>
                    <?php endif; ?>
                </div>

                <div class="status <?php echo $payment_status; ?>">
                    <strong>Payment Status:</strong> <?php echo ucfirst($payment_status); ?>
                </div>
            <?php else: ?>
                <p>No transaction data found.</p>
            <?php endif; ?>

            <p><a href="?action=form">← Make Another Payment</a></p>
        </div>
    </body>
    </html>
    <?php
}

/**
 * Show Cancel Page
 */
function showCancel() {
    $transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';
    $transaction = isset($_SESSION['transaction']) ? $_SESSION['transaction'] : null;

    logMessage("Payment cancelled for transaction: " . $transaction_id);

    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Cancelled - Ziina</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9; }
            .cancelled { color: #dc3545; text-align: center; }
            .info { background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0; }
        </style>
    </head>
    <body>
        <div class="card">
            <div class="cancelled">
                <h2>✗ Payment Cancelled</h2>
                <p>Your payment has been cancelled. No charges were made.</p>
            </div>

            <?php if ($transaction): ?>
                <div class="info">
                    <p><strong>Transaction ID:</strong> <?php echo htmlspecialchars($transaction['id']); ?></p>
                    <p><strong>Amount:</strong> <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?></p>
                    <p><strong>Description:</strong> <?php echo htmlspecialchars($transaction['description']); ?></p>
                </div>
            <?php endif; ?>

            <p><a href="?action=form">← Try Again</a></p>
        </div>
    </body>
    </html>
    <?php
}