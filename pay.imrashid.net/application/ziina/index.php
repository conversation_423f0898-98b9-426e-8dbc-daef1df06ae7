<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Ziina Payment Gateway Integration
 *
 * This file serves as the main entry point for Ziina payment integration
 *
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

// Prevent direct access
if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

// Include required files
require_once(APPPATH . 'ziina/config/ziina_config.php');
require_once(APPPATH . 'ziina/libraries/ZiinaAPI.php');
require_once(APPPATH . 'ziina/libraries/ZiinaPayment.php');
require_once(APPPATH . 'ziina/helpers/ziina_helper.php');

/**
 * Initialize Ziina Payment Gateway
 */
class ZiinaGateway {

    private $CI;

    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->helper('url');
        $this->CI->load->library('session');
        $this->CI->load->database();
    }

    /**
     * Get Ziina Gateway Instance
     */
    public static function getInstance() {
        static $instance = null;
        if ($instance === null) {
            $instance = new self();
        }
        return $instance;
    }
}

// Auto-load Ziina Gateway
$ziina_gateway = ZiinaGateway::getInstance();