<?php
$api_key = 'your_ziina_api_key';
$base_url = 'https://api.ziina.com'; // Placeholder; use the real endpoint

$data = [
    "amount" => 10000, // in fils or minor currency unit
    "currency" => "AED",
    "description" => "Order #1234",
    "customer" => [
        "name" => "John Doe",
        "email" => "<EMAIL>"
    ],
    "callback_url" => "https://yourdomain.com/payment-callback",
];

$ch = curl_init("$base_url/v1/payments");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: Bearer $api_key",
    "Content-Type: application/json"
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
$response = curl_exec($ch);
curl_close($ch);

$result = json_decode($response, true);

if (isset($result['payment_url'])) {
    header("Location: " . $result['payment_url']);
    exit;
} else {
    echo "Payment initialization failed.";
}