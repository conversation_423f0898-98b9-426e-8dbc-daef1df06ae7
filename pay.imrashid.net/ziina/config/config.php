<?php
/**
 * Ziina Payment Gateway Configuration
 *
 * Configuration settings for Ziina payment integration
 *
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

// Ziina API Configuration
define('ZIINA_SANDBOX_URL', 'https://api.sandbox.ziina.com');
define('ZIINA_PRODUCTION_URL', 'https://api.ziina.com');

// Ziina API Endpoints
define('ZIINA_PAYMENT_INTENT_ENDPOINT', '/v1/payment-intents');
define('ZIINA_PAYMENT_CONFIRM_ENDPOINT', '/v1/payment-intents/{id}/confirm');
define('ZIINA_PAYMENT_CANCEL_ENDPOINT', '/v1/payment-intents/{id}/cancel');
define('ZIINA_PAYMENT_RETRIEVE_ENDPOINT', '/v1/payment-intents/{id}');
define('ZIINA_WEBHOOK_ENDPOINT', '/v1/webhooks');

// Ziina Payment Methods
define('ZIINA_PAYMENT_METHOD_CARD', 'card');
define('ZIINA_PAYMENT_METHOD_WALLET', 'wallet');
define('ZIINA_PAYMENT_METHOD_BANK_TRANSFER', 'bank_transfer');

// Ziina Payment Status
define('ZIINA_STATUS_PENDING', 'pending');
define('ZIINA_STATUS_PROCESSING', 'processing');
define('ZIINA_STATUS_SUCCEEDED', 'succeeded');
define('ZIINA_STATUS_FAILED', 'failed');
define('ZIINA_STATUS_CANCELED', 'canceled');
define('ZIINA_STATUS_REQUIRES_ACTION', 'requires_action');

// Ziina Currency Codes
define('ZIINA_CURRENCY_AED', 'AED');
define('ZIINA_CURRENCY_USD', 'USD');
define('ZIINA_CURRENCY_EUR', 'EUR');

// Application Settings
define('ZIINA_SANDBOX_MODE', true); // Set to false for production
define('ZIINA_LOG_ENABLED', true);
define('ZIINA_LOG_FILE', ZIINA_BASE_DIR . '/logs/ziina.log');

// Default Ziina API Credentials (Override in admin panel)
define('ZIINA_DEFAULT_API_KEY', 'your_api_key_here');
define('ZIINA_DEFAULT_SECRET_KEY', 'your_secret_key_here');

// Application URLs
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_path = dirname($_SERVER['SCRIPT_NAME']);

define('ZIINA_BASE_URL', $protocol . '://' . $host . $base_path);
define('ZIINA_SUCCESS_URL', ZIINA_BASE_URL . '/success');
define('ZIINA_CANCEL_URL', ZIINA_BASE_URL . '/cancel');
define('ZIINA_WEBHOOK_URL', ZIINA_BASE_URL . '/webhook');

// Supported currencies and their minimum amounts
$ZIINA_CURRENCIES = array(
    ZIINA_CURRENCY_AED => array(
        'name' => 'UAE Dirham',
        'symbol' => 'د.إ',
        'min_amount' => 1.00,
        'decimal_places' => 2
    ),
    ZIINA_CURRENCY_USD => array(
        'name' => 'US Dollar',
        'symbol' => '$',
        'min_amount' => 0.50,
        'decimal_places' => 2
    ),
    ZIINA_CURRENCY_EUR => array(
        'name' => 'Euro',
        'symbol' => '€',
        'min_amount' => 0.50,
        'decimal_places' => 2
    )
);

// Supported payment methods
$ZIINA_PAYMENT_METHODS = array(
    ZIINA_PAYMENT_METHOD_CARD => array(
        'name' => 'Credit/Debit Card',
        'description' => 'Pay with your credit or debit card',
        'icon' => 'card'
    ),
    ZIINA_PAYMENT_METHOD_WALLET => array(
        'name' => 'Digital Wallet',
        'description' => 'Pay with your digital wallet',
        'icon' => 'wallet'
    ),
    ZIINA_PAYMENT_METHOD_BANK_TRANSFER => array(
        'name' => 'Bank Transfer',
        'description' => 'Pay via bank transfer',
        'icon' => 'bank'
    )
);

// Error messages
$ZIINA_ERROR_MESSAGES = array(
    'invalid_api_key' => 'Invalid API key provided',
    'invalid_amount' => 'Invalid payment amount',
    'invalid_currency' => 'Invalid or unsupported currency',
    'invalid_payment_method' => 'Invalid payment method',
    'payment_failed' => 'Payment processing failed',
    'payment_declined' => 'Payment was declined',
    'insufficient_funds' => 'Insufficient funds',
    'card_expired' => 'Card has expired',
    'invalid_card' => 'Invalid card details',
    'network_error' => 'Network connection error',
    'webhook_verification_failed' => 'Webhook signature verification failed',
    'transaction_not_found' => 'Transaction not found',
    'invalid_request' => 'Invalid request parameters'
);

// Helper functions
function ziina_get_currency_info($currency_code) {
    global $ZIINA_CURRENCIES;
    return isset($ZIINA_CURRENCIES[$currency_code]) ? $ZIINA_CURRENCIES[$currency_code] : null;
}

function ziina_get_payment_method_info($method) {
    global $ZIINA_PAYMENT_METHODS;
    return isset($ZIINA_PAYMENT_METHODS[$method]) ? $ZIINA_PAYMENT_METHODS[$method] : null;
}

function ziina_get_error_message($error_code) {
    global $ZIINA_ERROR_MESSAGES;
    return isset($ZIINA_ERROR_MESSAGES[$error_code]) ? $ZIINA_ERROR_MESSAGES[$error_code] : 'Unknown error';
}

function ziina_format_amount($amount, $currency) {
    $currency_info = ziina_get_currency_info($currency);
    if ($currency_info) {
        return number_format($amount, $currency_info['decimal_places']);
    }
    return number_format($amount, 2);
}

function ziina_log($message, $level = 'INFO') {
    if (!ZIINA_LOG_ENABLED) {
        return;
    }
    
    $log_dir = dirname(ZIINA_LOG_FILE);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    file_put_contents(ZIINA_LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
}

function ziina_generate_transaction_id() {
    return 'ZN_' . time() . '_' . mt_rand(1000, 9999);
}

function ziina_validate_amount($amount, $currency) {
    if (!is_numeric($amount) || $amount <= 0) {
        return false;
    }
    
    $currency_info = ziina_get_currency_info($currency);
    if (!$currency_info) {
        return false;
    }
    
    return $amount >= $currency_info['min_amount'];
}

function ziina_is_valid_currency($currency) {
    global $ZIINA_CURRENCIES;
    return isset($ZIINA_CURRENCIES[strtoupper($currency)]);
}

function ziina_is_valid_payment_method($method) {
    global $ZIINA_PAYMENT_METHODS;
    return isset($ZIINA_PAYMENT_METHODS[$method]);
}

// Create logs directory if it doesn't exist
$log_dir = dirname(ZIINA_LOG_FILE);
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}
