<?php
/**
 * Database Class for Ziina Payment Gateway
 * 
 * Simple PDO wrapper for database operations
 * 
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

class Database {
    
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, array(
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ));
        } catch (PDOException $e) {
            ziina_log("Database connection failed: " . $e->getMessage(), 'ERROR');
            throw new Exception("Database connection failed");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * Execute a query and return results
     */
    public function query($sql, $params = array()) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            ziina_log("Query failed: " . $e->getMessage() . " SQL: " . $sql, 'ERROR');
            throw new Exception("Database query failed");
        }
    }
    
    /**
     * Insert data and return last insert ID
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($data);
            return $this->connection->lastInsertId();
        } catch (PDOException $e) {
            ziina_log("Insert failed: " . $e->getMessage() . " SQL: " . $sql, 'ERROR');
            throw new Exception("Database insert failed");
        }
    }
    
    /**
     * Update data
     */
    public function update($table, $data, $where, $whereParams = array()) {
        $setParts = array();
        foreach (array_keys($data) as $key) {
            $setParts[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        
        try {
            $stmt = $this->connection->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            ziina_log("Update failed: " . $e->getMessage() . " SQL: " . $sql, 'ERROR');
            throw new Exception("Database update failed");
        }
    }
    
    /**
     * Delete data
     */
    public function delete($table, $where, $params = array()) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        try {
            $stmt = $this->connection->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            ziina_log("Delete failed: " . $e->getMessage() . " SQL: " . $sql, 'ERROR');
            throw new Exception("Database delete failed");
        }
    }
    
    /**
     * Get single row
     */
    public function getRow($sql, $params = array()) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Get all rows
     */
    public function getRows($sql, $params = array()) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get single value
     */
    public function getValue($sql, $params = array()) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * Check if table exists
     */
    public function tableExists($table) {
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->getValue($sql, array('table' => $table));
        return $result !== false;
    }
    
    /**
     * Create tables if they don't exist
     */
    public function createTables() {
        // Create ziina_transactions table
        if (!$this->tableExists('ziina_transactions')) {
            $sql = "
                CREATE TABLE ziina_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    transaction_id VARCHAR(100) UNIQUE NOT NULL,
                    payment_intent_id VARCHAR(100),
                    amount DECIMAL(10,2) NOT NULL,
                    currency VARCHAR(3) NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    payment_method VARCHAR(50),
                    customer_email VARCHAR(255),
                    customer_name VARCHAR(255),
                    description TEXT,
                    metadata JSON,
                    payment_response JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_transaction_id (transaction_id),
                    INDEX idx_payment_intent_id (payment_intent_id),
                    INDEX idx_status (status),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $this->query($sql);
            ziina_log("Created ziina_transactions table", 'INFO');
        }
        
        // Create ziina_config table
        if (!$this->tableExists('ziina_config')) {
            $sql = "
                CREATE TABLE ziina_config (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    config_key VARCHAR(100) UNIQUE NOT NULL,
                    config_value TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_config_key (config_key)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $this->query($sql);
            ziina_log("Created ziina_config table", 'INFO');
            
            // Insert default configuration
            $defaultConfig = array(
                array('api_key', ZIINA_DEFAULT_API_KEY, 'Ziina API Key'),
                array('secret_key', ZIINA_DEFAULT_SECRET_KEY, 'Ziina Secret Key'),
                array('sandbox_mode', ZIINA_SANDBOX_MODE ? '1' : '0', 'Sandbox Mode (1 = enabled, 0 = disabled)'),
                array('webhook_secret', '', 'Webhook Secret Key'),
                array('default_currency', ZIINA_CURRENCY_AED, 'Default Currency'),
                array('company_name', 'Your Company', 'Company Name'),
                array('support_email', '<EMAIL>', 'Support Email')
            );
            
            foreach ($defaultConfig as $config) {
                $this->insert('ziina_config', array(
                    'config_key' => $config[0],
                    'config_value' => $config[1],
                    'description' => $config[2]
                ));
            }
        }
        
        // Create ziina_webhooks table
        if (!$this->tableExists('ziina_webhooks')) {
            $sql = "
                CREATE TABLE ziina_webhooks (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    event_type VARCHAR(100) NOT NULL,
                    payment_intent_id VARCHAR(100),
                    payload JSON NOT NULL,
                    signature VARCHAR(255),
                    processed TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_at TIMESTAMP NULL,
                    INDEX idx_event_type (event_type),
                    INDEX idx_payment_intent_id (payment_intent_id),
                    INDEX idx_processed (processed),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $this->query($sql);
            ziina_log("Created ziina_webhooks table", 'INFO');
        }
    }
}

// Initialize database and create tables
try {
    $db = Database::getInstance();
    $db->createTables();
} catch (Exception $e) {
    ziina_log("Database initialization failed: " . $e->getMessage(), 'ERROR');
}
