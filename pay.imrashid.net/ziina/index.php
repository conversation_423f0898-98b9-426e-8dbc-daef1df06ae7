<?php
/**
 * Ziina Payment Gateway Integration - Core PHP
 * 
 * Main entry point for Ziina payment processing
 * 
 * @package    Ziina Payment Gateway
 * <AUTHOR> Name
 * @version    1.0.0
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Define base directory
define('ZIINA_BASE_DIR', __DIR__);

// Include required files
require_once(ZIINA_BASE_DIR . '/config/config.php');
require_once(ZIINA_BASE_DIR . '/includes/Database.php');
require_once(ZIINA_BASE_DIR . '/includes/ZiinaAPI.php');
require_once(ZIINA_BASE_DIR . '/includes/ZiinaPayment.php');
require_once(ZIINA_BASE_DIR . '/includes/helpers.php');

// Handle routing
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];
$path = str_replace(dirname($script_name), '', $request_uri);
$path = trim($path, '/');

// Remove query string
if (($pos = strpos($path, '?')) !== false) {
    $path = substr($path, 0, $pos);
}

// Route requests
switch ($path) {
    case '':
    case 'index.php':
        require_once(ZIINA_BASE_DIR . '/views/dashboard.php');
        break;
        
    case 'payment':
        require_once(ZIINA_BASE_DIR . '/payment.php');
        break;
        
    case 'webhook':
        require_once(ZIINA_BASE_DIR . '/webhook.php');
        break;
        
    case 'success':
        require_once(ZIINA_BASE_DIR . '/success.php');
        break;
        
    case 'cancel':
        require_once(ZIINA_BASE_DIR . '/cancel.php');
        break;
        
    case 'config':
        require_once(ZIINA_BASE_DIR . '/admin/config.php');
        break;
        
    default:
        http_response_code(404);
        echo "Page not found";
        break;
}
