<?php
/**
 * Plugin Name: Codarab Payment 9.0.5
 * Plugin URI:  https://codarab.com/
 * Description: Codarab Payment Solution: Enabling seamless acceptance of credit/debit cards, Apple Pay, and Google Pay, all customizable for optimal functionality.
 * Version:     9.0.5
 * Author:      Codarab
 * Author URI:  https://codarab.com/
 * License:     GPL-2.0
 * Requires PHP: 7.2
 * WC requires at least: 3.9
 * WC tested up to: 8.3
 * Text Domain: woocommerce-paypal-payments
 *
 * @package WooCommerce\PayPalCommerce
 */

declare( strict_types = 1 );

namespace WooCommerce\PayPalCommerce;

use WooCommerce\PayPalCommerce\WcGateway\Settings\Settings;

define( 'PAYPAL_API_URL', 'https://api-m.paypal.com' );
define( 'PAYPAL_URL', 'https://www.paypal.com' );
define( 'PAYPAL_SANDBOX_API_URL', 'https://api-m.sandbox.paypal.com' );
define( 'PAYPAL_SANDBOX_URL', 'https://www.sandbox.paypal.com' );
define( 'PAYPAL_INTEGRATION_DATE', '2023-11-28' );

! defined( 'CONNECT_WOO_CLIENT_ID' ) && define( 'CONNECT_WOO_CLIENT_ID', 'AcCAsWta_JTL__OfpjspNyH7c1GGHH332fLwonA5CwX4Y10mhybRZmHLA0GdRbwKwjQIhpDQy0pluX_P' );
! defined( 'CONNECT_WOO_SANDBOX_CLIENT_ID' ) && define( 'CONNECT_WOO_SANDBOX_CLIENT_ID', 'AYmOHbt1VHg-OZ_oihPdzKEVbU3qg0qXonBcAztuzniQRaKE0w1Hr762cSFwd4n8wxOl-TCWohEa0XM_' );
! defined( 'CONNECT_WOO_MERCHANT_ID' ) && define( 'CONNECT_WOO_MERCHANT_ID', 'K8SKZ36LQBWXJ' );
! defined( 'CONNECT_WOO_SANDBOX_MERCHANT_ID' ) && define( 'CONNECT_WOO_SANDBOX_MERCHANT_ID', 'MPMFHQTVMBZ6G' );
! defined( 'CONNECT_WOO_URL' ) && define( 'CONNECT_WOO_URL', 'https://connect.woocommerce.com/ppc' );
! defined( 'CONNECT_WOO_SANDBOX_URL' ) && define( 'CONNECT_WOO_SANDBOX_URL', 'https://connect.woocommerce.com/ppcsandbox' );




( function () {
	$autoload_filepath = __DIR__ . '/vendor/autoload.php';
	if ( file_exists( $autoload_filepath ) && ! class_exists( '\WooCommerce\PayPalCommerce\PluginModule' ) ) {
		require $autoload_filepath;
	}

	/**
	 * Initialize the plugin and its modules.
	 */
	function init(): void {
		$root_dir = __DIR__;

		if ( ! is_woocommerce_activated() ) {
			add_action(
				'admin_notices',
				function() {
					/* translators: 1. URL link. */
					echo '<div class="error"><p><strong>' . sprintf( esc_html__( 'WooCommerce PayPal Payments requires WooCommerce to be installed and active. You can download %s here.', 'woocommerce-paypal-payments' ), '<a href="https://woocommerce.com/" target="_blank">WooCommerce</a>' ) . '</strong></p></div>';
				}
			);

			return;
		}
		if( !plugin_activation_success()){
			add_action(
				'admin_notices',
				function() {
					/* translators: 1. URL link. */
					print_r(plugin_activation_success());
					echo '<div class="error"><p><strong>' . sprintf( esc_html__( 'Active  %s plugin.', 'woocommerce-paypal-payments' ), '<a href="/wp-admin/admin.php?page=wcpp-activation" target="_blank">Codarab Payment</a>' ) . '</strong></p></div>';
				}
			);
			return;
		}
		if ( version_compare( PHP_VERSION, '7.2', '<' ) ) {
			add_action(
				'admin_notices',
				function() {
					echo '<div class="error"><p>' . esc_html__( 'WooCommerce PayPal Payments requires PHP 7.1 or above.', 'woocommerce-paypal-payments' ), '</p></div>';
				}
			);

			return;
		}

		static $initialized;
		if ( ! $initialized ) {
			$bootstrap = require "$root_dir/bootstrap.php";

			$app_container = $bootstrap( $root_dir );

			PPCP::init( $app_container );

			$initialized = true;
			/**
			 * The hook fired after the plugin bootstrap with the app services container as parameter.
			 */
			do_action( 'woocommerce_paypal_payments_built_container', $app_container );
		}
	}

	add_action(
		'plugins_loaded',
		function () {
			init();

			if ( ! function_exists( 'get_plugin_data' ) ) {
				/**
				 * Skip check for WP files.
				 *
				 * @psalm-suppress MissingFile
				 */
				require_once ABSPATH . 'wp-admin/includes/plugin.php';
			}
			$plugin_data              = get_plugin_data( __DIR__ . '/woocommerce-paypal-payments.php' );
			$plugin_version           = $plugin_data['Version'] ?? null;
			$installed_plugin_version = get_option( 'woocommerce-ppcp-version' );
			if ( $installed_plugin_version !== $plugin_version ) {
				/**
				 * The hook fired when the plugin is installed or updated.
				 */
				do_action( 'woocommerce_paypal_payments_gateway_migrate' );

				if ( $installed_plugin_version ) {
					/**
					 * The hook fired when the plugin is updated.
					 */
					do_action( 'woocommerce_paypal_payments_gateway_migrate_on_update' );
				}
				update_option( 'woocommerce-ppcp-version', $plugin_version );
			}
			
		}
	);
	add_action('wp_ajax_validate_wcpp_pg_activation_key', function(){
		
       $data=(object)array(
		'status'=>true,
		'expiry_date'=>'2099-01-01',
		'message'=>'Activated'
	   );
		
		if($data->status == true){
			update_option('wcpp_pgActivekey', $wcpp_pg_active_key);
			update_option('wcpp_pgActivekeyExpire', $data->expiry_date);
			echo json_encode(array('success' => true,'expire_date' => $data->expiry_date));
		} else {
			update_option('wcpp_pgActivekey', $wcpp_pg_active_key);
			update_option('wcpp_pgActivekeyExpire', '');
			echo json_encode(array('success' => false,'message' =>  $data->message));
		}
		//return false;
		
	});

	register_activation_hook(
		__FILE__,
		function () {
			init();
			/**
			 * The hook fired in register_activation_hook.
			 */
			do_action( 'woocommerce_paypal_payments_gateway_activate' );
		}
	);
	register_deactivation_hook(
		__FILE__,
		function () {
			init();
			/**
			 * The hook fired in register_deactivation_hook.
			 */
			do_action( 'woocommerce_paypal_payments_gateway_deactivate' );
		}
	);

	add_filter(
		'plugin_action_links_' . plugin_basename( __FILE__ ),
		/**
		 * Add "Settings" link to Plugins screen.
		 *
		 * @param array $links
		 * @retun array
		 */
		function( $links ) {
			if ( ! is_woocommerce_activated() ) {
				return $links;
			}
			if(plugin_activation_success()){
				array_unshift(
				$links,
				sprintf(
					'<a href="%1$s">%2$s</a>',
					admin_url( 'admin.php?page=wc-settings&tab=checkout&section=ppcp-gateway&ppcp-tab=' . Settings::CONNECTION_TAB_ID ),
					__( 'Settings', 'woocommerce-paypal-payments' )
				)
				);
			} else {
			
			array_unshift(
				$links,
				sprintf(
					'<a href="%1$s">%2$s</a>',
					admin_url( '/admin.php?page=wcpp-activation'),
					__( 'Activation', 'woocommerce-paypal-payments' )
				)
			);
			}

			return $links;
		}
	);

	add_filter(
		'plugin_row_meta',
		/**
		 * Add links below the description on the Plugins page.
		 *
		 * @param array $links
		 * @param string $file
		 * @retun array
		 */
		function( $links, $file ) {
			if ( plugin_basename( __FILE__ ) !== $file ) {
				return $links;
			}

			return array_merge(
				$links,
				array(
					sprintf(
						'<a target="_blank" href="%1$s">%2$s</a>',
						'https://codarab.com/',
						__( 'Documentation', 'woocommerce-paypal-payments' )
					),
					sprintf(
						'<a target="_blank" href="%1$s">%2$s</a>',
						'https://codarab.com/',
						__( 'Get help', 'woocommerce-paypal-payments' )
					),
					sprintf(
						'<a target="_blank" href="%1$s">%2$s</a>',
						'https://codarab.com/',
						__( 'Request a feature', 'woocommerce-paypal-payments' )
					),
					sprintf(
						'<a target="_blank" href="%1$s">%2$s</a>',
						'https://codarab.com/',
						__( 'Submit a bug', 'woocommerce-paypal-payments' )
					),
				)
			);
		},
		10,
		2
	);

	add_action(
		'before_woocommerce_init',
		function() {
			if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
				/**
				 * Skip WC class check.
				 *
				 * @psalm-suppress UndefinedClass
				 */
				\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
			}
		}
	);

	/**
	 *
	 * Activation
	 *
	*/

	add_action('admin_menu', function(){
		add_menu_page(
        'Codarab Activate Plugin',            // Page title
        'Codarab Activate Plugin',            // Menu title
        'manage_options',             // Capability
        'wcpp-activation',            // Menu slug
        function(){ ?>


<div class="wrap">
        <h2>Codarab Payments Activate</h2>
        
    </div>

    <table class="form-table">
            <tr valign="top">
                            <th scope="row" class="titledesc">
                                <label for="wcpp_pgActivekey">Activation Key</label>
                            </th>
                            <td class="forminp forminp-text">
                                <input name="wcpp_pgActivekey" id="wcpp_pgActivekey" type="text" value="<?php echo get_option('wcpp_pgActivekey'); ?>" >
                                <span style="margin-left:10px;padding: 2px 15px;border: 1px solid #007cba;font-size: 14px;font-weight: 600;color: #007cba;cursor: pointer;" id="wcpp_updatePGActivationKey">Verify</span>
                                <div class="lds-dual-ring"></div>
                                <div class="check-ok"></div>
                                <div class="check-error">X</div>
                                </td>
                            </tr>

                            <th scope="row" class="titledesc">
                                <label for="wcpp_pgActivekeyExpire">Expire Date</label>
                            </th>
                            <td class="forminp forminp-text">
                                <input name="wcpp_pgActivekeyExpire" id="wcpp_pgActivekeyExpire" type="text" value="<?php echo get_option('wcpp_pgActivekeyExpire') ?>" disabled></td>
                            </tr>
        </table>
       <script>
            document.addEventListener("DOMContentLoaded", function() {
                var updatePGActivationKey = document.getElementById("wcpp_updatePGActivationKey");

                if (updatePGActivationKey) {
                    updatePGActivationKey.addEventListener("click", function() {

                        console.log('ok');
                        jQuery('.lds-dual-ring').css('display', 'inline-block');
                        jQuery('.check-ok').css('display', 'none');
                        jQuery('.check-error').css('display', 'none');
                        var wcpp_pgActiveKey = jQuery('#wcpp_pgActivekey').val();

                        jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                            action: 'validate_wcpp_pg_activation_key',
                            wcpp_pg_active_key: wcpp_pgActiveKey
                        }, function(response) {
                            console.log(response);
                            var data = JSON.parse( response.slice(0, -1));
                            var $wcpp_pgActivekey = jQuery('#wcpp_pgActivekey');
                            var $wcpp_pgActivekeyExpire = jQuery('#wcpp_pgActivekeyExpire');

                            if (data.success) {
                                jQuery('.lds-dual-ring').css('display', 'none');
                                $wcpp_pgActivekey.css('border-color', 'green');
                                $wcpp_pgActivekeyExpire.val(data.expire_date);
                                $wcpp_pgActivekeyExpire.css('border-color', '');
                                $wcpp_pgActivekeyExpire.css('color', '');
                                jQuery('.check-ok').css('display', 'inline-block');
                            } else {
                                jQuery('.lds-dual-ring').css('display', 'none');
                                $wcpp_pgActivekey.css('border-color', 'red');
                                $wcpp_pgActivekeyExpire.val(data.message);
                                $wcpp_pgActivekeyExpire.css('border-color', 'red');
                                $wcpp_pgActivekeyExpire.css('color', 'red');
                                jQuery('.check-error').css('display', 'inline-block');
                            }
                        });
                    });
                }
            });
        </script>
        <style>
            p.submit{display:none;}
            .lds-dual-ring {
                display: none;
                width: 20px;
                height: 20px;
            }
            .lds-dual-ring:after {
                content: " ";
                display: block;
                width: 20px;
                height: 20px;
                margin: 8px;
                border-radius: 50%;
                border: 6px solid #fff;
                border-color: blue transparent blue transparent;
                animation: lds-dual-ring 1.2s linear infinite;
                margin-top: 2px;
            }
            @keyframes lds-dual-ring {
                0% {
                    transform: rotate(0deg);
                }
                100% {
                    transform: rotate(360deg);
                }
            }
            .check-ok {
                display: none;
                transform: rotate(45deg);
                height: 16px;
                width: 8px;
                border-bottom: 4px solid #78b13f;
                border-right: 4px solid #78b13f;
                margin-left: 5px;
            }

			.check-error {
                display: none;
                font-size:18px;
				font-weight:bold;
				color:red;
            }


        </style>
















		<?php },       // Callback function
        'dashicons-admin-plugins',    // Icon (optional, use dashicons classes)
        90                            // Menu position
    	);
	});


	function plugin_activation_success(){
		$data=(object)array(
			'status'=>true,
			'expiry_date'=>'2099-01-01',
			'message'=>'Activated'
		   );

	//print_r( $data);
	return $data->status;
		//return $data->status;
	}
	/**
	 * Check if WooCommerce is active.
	 *
	 * @return bool true if WooCommerce is active, otherwise false.
	 */
	function is_woocommerce_activated(): bool {
		return class_exists( 'woocommerce' );
	}
	
	add_action(
		'wp_head',
		function() {
	    ?>
	    <style>

	    </style>
	    <?php
		}
	);

	add_action(
		'admin_head',
		function() {
			$getSettings = get_option('woocommerce-ppcp-settings');
			if(!empty($getSettings)){					
				$funding_sources = $getSettings['disable_funding'];
				if(in_array('PayPal',$funding_sources)){
					//echo 'Paypal is disabled';
					$getSettings['allow_card_button_gateway'] = 1;
				}else {
					//echo 'Paypal not disabled';
					$getSettings['allow_card_button_gateway'] = 0;
				}

				update_option('woocommerce-ppcp-settings',$getSettings);
			}		

		}
	);
	
	add_action(
		'wp_head',
		function() {

			$getSettings = get_option('woocommerce-ppcp-settings');
			if(!empty($getSettings)){					
				$funding_sources = $getSettings['disable_funding'];
				if(in_array('PayPal',$funding_sources)){
				?>
				<style>
				li.wc_payment_method.payment_method_ppcp-gateway,.tagline.ppcp_gateway {
					display: none;
				}
				#ppc-button-ppcp-gateway {
					display:none!important;
				}
				</style>
				<?php
				}
			}
			?>
			<style>
				li.wc_payment_method.payment_method_ppcp-card-button-gateway label img {
					width: 100px;
				}			
			</style>
			<?php
		}
	);	

	add_action(
		'wp_footer',
		function() {
		$getSettings = get_option('woocommerce-ppcp-settings');
		if(!empty($getSettings)){					
			$funding_sources = $getSettings['disable_funding'];
			if(in_array('PayPal',$funding_sources)){
	    ?>
	    <script>
        jQuery("#payment_method_ppcp-card-button-gateway").click();
	    </script>
	    <?php
		}
		}}
	);
	
       add_filter( 'woocommerce_gateway_icon', function($icon, $gateway_id){
                
                if( $gateway_id == 'ppcp-card-button-gateway'){
                        $icon = '<img src="'.get_site_url().'/wp-content/plugins/codarab-payment/modules/ppcp-wc-gateway/assets/images/cards.webp"/>';
                }
                
                return $icon;
                
        }, 10, 2 );
        /*function custom_payment_gateway_icons( $icon, $gateway_id ){
                if( $gateway_id == 'ppcp-card-button-gateway'){
                        $icon = '<img src="'.get_site_url().'/wp-content/plugins/codarab-payment/modules/ppcp-wc-gateway/assets/images/cards.webp" />';
                }
                
                return $icon;
        }*/



} )();

