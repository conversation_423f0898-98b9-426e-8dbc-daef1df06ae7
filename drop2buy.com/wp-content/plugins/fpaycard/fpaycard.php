<?php 
/*
 * Plugin Name: FPayCard
 * Plugin URI: https://fusionpay.online
 * Description: Take credit card payments for your store from a proxy site.
 * Author: FPayCard
 * Author URI: https://fusionpay.online
 * Text Domain: fpaycard
 * Version: 1.0.1
 */

if (!defined('FPAYPCC_ROOT_URL')) {
	define('FPAYPCC_ROOT_URL',  'https://fusionpay.online/fpaypcc/');
}

add_action("plugins_loaded", "fpaypcc_load");
function fpaypcc_load()
{
    class Fpaypcc_Gateway extends WC_Payment_Gateway_CC
    {
        public function __construct()
        {
            $this->id = "fpaypcc";
            $this->method_title = __("Credit Card");
            $this->method_description = __("ON-Page Checkout by fPaypcc ");
            $this->has_fields = true;
            $this->supports = array(
                "products",
                "refunds"
            );
            $this->title = "FPAYPCC";
            $this->description = " ";
            $this->enabled = true;
            add_action("wp_enqueue_scripts", array(
                $this,
                "fpaypcc_payment_scripts"
            ));
            $this->init_form_fields();
            $this->init_settings();
            $this->label = $this->get_option("label");
            $this->icon = $this->get_option("icon");
            add_action("woocommerce_update_options_payment_gateways_{$this->id}", array(
                $this,
                "process_admin_options"
            ));
        }
        public function fpaypcc_register_gateway($methods)
        {
            $methods[] = "Fpaypcc_Gateway";
            return $methods;
        }
        public function get_headers()
        {
            $headers = array();
            $headers[] = "Content-Type: application/x-www-form-urlencoded";
            return $headers;
        }
        public function fpaypcc_request_auth($params)
        {
            $ch = curl_init();
            $headers = self::get_headers();
            $url = FPAYPCC_ROOT_URL . "api";
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params, true));
            $result = curl_exec($ch);
            $result = json_decode($result);
            curl_close($ch);
            return $result;
        }
        public function init_form_fields()
        {
            $this->form_fields = array(
                "label" => array(
                    "title" => __("Label", "fpaypcc") ,
                    "type" => "text",
                    "description" => __("This controls the title which the user sees during checkout.", "fpaypcc") ,
                    "default" => "Secure payment",
                    "desc_tip" => true
                ) ,
                "icon" => array(
                    "title" => __("Icon", "fpaypcc") ,
                    "type" => "text",
                    "description" => __("This controls the icon which the user sees during checkout.", "fpaypcc") ,
                    "default" => "https://static1.s123-cdn-static-a.com/admin/websitesPluginsManager/800_5b8530a55bae9.jpg",
                    "desc_tip" => true
                )
            );
        }
        public function payment_fields()
        {
            ob_start();
            echo "<style> .payment_method_fpaypcc label {display:none !important;} </style>";
            if ($this->get_option("label") != '')
            {
                echo "<h3>" . $this->get_option("label") . "</h3>";
            }
            if ($this->get_option("icon") != '')
            {
                echo "<img src='" . $this->get_option("icon") . "'><br/><br/>";
            }
            echo "<div class='card - wrapper'></div>";
            echo " <div class='fpaypcc - source - errors' role='alert'></div>";
            $this->form();
            ob_end_flush();
        }
        public function fpaypcc_payment_scripts()
        {
            if (!is_product() && !is_cart() && !is_checkout())
            {
                return;
            }
            wp_enqueue_script("jquery-payment");
            wp_enqueue_script("wc-credit-card-form");
        }
        public function process_payment($order_id)
        {
            $order = wc_get_order($order_id);
            $order_data = $order->get_data();
            $order_items = array();
            foreach ($order->get_items() as $item_key => $item)
            {
                $item_id = $item->get_id();
                $item_data = $item->get_data();
                $product = $item->get_product();
                $product = array(
                    "product_type" => $product->get_type() ,
                    "product_sku" => $product->get_sku() ,
                    "product_price" => $product->get_price() ,
                    "stock_quantity" => $product->get_stock_quantity()
                );
                array_push($order_items, array(
                    "product" => $product,
                    "item_data" => $item_data
                ));
            }
            $card_info = array(
                "fpaypcc-card-number" => $_POST["fpaypcc-card-number"],
                "fpaypcc-card-expiry" => $_POST["fpaypcc-card-expiry"],
                "fpaypcc-card-cvc" => $_POST["fpaypcc-card-cvc"]
            );
            $params = array(
                "client_url" => home_url() ,
                "order_data" => $order_data,
                "order_items" => $order_items,
                "card_info" => $card_info
            );
            $params = base64_encode(serialize(array(
                "client_url" => home_url() ,
                "order_data" => serialize($order_data) ,
                "order_items" => serialize($order_items) ,
                "card_info" => serialize($card_info)
            )));
            try
            {
                $result = self::fpaypcc_request_auth(array(
                    "params" => $params
                ));
                
                if ($result->status == "succeeded")
                {
                    $note_text = "Payment Received. Ref: " . $result->transaction_id . "/" . $result->auth_code;
                    update_post_meta($order_id, "_transaction_id", $result->transaction_id);
                    $order->update_status("processing", sprintf(__("FPAYPCC charge payment: %s.", "fpaypcc") , $result->transaction_id));
                    return array(
                        "result" => "success",
                        "redirect" => $this->get_return_url($order)
                    );
                }
                else
                {
                    wc_add_notice($result->message,'error');
                    return array(
                        "result" => "failure",
                        "redirect" => ''
                    );
                }
            }
            catch(\Throwable $th)
            {
                file_put_contents("fpaypcc_process_payment_errors.log", print_r(array(
                    "catch" => $th,
                    "params" => $params
                ) , true));
            }
            return array(
                "result" => "failure",
                "redirect" => ''
            );
        }
    }
    $fpaypcc_gateway_instance = new Fpaypcc_Gateway();
    add_filter("woocommerce_payment_gateways", array(
        $fpaypcc_gateway_instance,
        "fpaypcc_register_gateway"
    ));
}
