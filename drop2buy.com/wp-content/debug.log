[25-Apr-2024 09:32:33 UTC] PHP Fatal error:  Uncaught Error: Cannot use object of type WP_Error as array in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php:453
Stack trace:
#0 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(63): WooCommerce\PayPalCommerce\plugin_activation_success()
#1 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(170): WooCommerce\PayPalCommerce\init()
#2 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(324): WooCommerce\PayPalCommerce\{closure}(false)
#3 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#5 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admin/includes/plugin.php(705): do_action('activate_codara...', false)
#6 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admi in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 453
[25-Apr-2024 09:32:54 UTC] PHP Fatal error:  Uncaught Error: Cannot use object of type WP_Error as array in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php:453
Stack trace:
#0 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(63): WooCommerce\PayPalCommerce\plugin_activation_success()
#1 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(170): WooCommerce\PayPalCommerce\init()
#2 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(324): WooCommerce\PayPalCommerce\{closure}(false)
#3 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#5 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admin/includes/plugin.php(705): do_action('activate_codara...', false)
#6 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admi in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 453
[25-Apr-2024 09:33:21 UTC] PHP Fatal error:  Uncaught Error: Cannot use object of type WP_Error as array in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php:453
Stack trace:
#0 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(63): WooCommerce\PayPalCommerce\plugin_activation_success()
#1 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(170): WooCommerce\PayPalCommerce\init()
#2 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(324): WooCommerce\PayPalCommerce\{closure}(false)
#3 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#5 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admin/includes/plugin.php(705): do_action('activate_codara...', false)
#6 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admi in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 453
[25-Apr-2024 09:35:31 UTC] PHP Notice:  Undefined property: wpdb::$wc_orders in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wpdb.php on line 783
[25-Apr-2024 09:35:31 UTC] WordPress database error You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'WHERE payment_method = 'ppec_paypal'' at line 1 for query SELECT 1 FROM  WHERE payment_method = 'ppec_paypal' made by activate_plugin, do_action('activate_codarab-payment/woocommerce-paypal-payments.php'), WP_Hook->do_action, WP_Hook->apply_filters, WooCommerce\PayPalCommerce\{closure}, WooCommerce\PayPalCommerce\init, {closure}, WooCommerce\PayPalCommerce\Compat\CompatModule->run, WooCommerce\PayPalCommerce\Compat\CompatModule->initialize_ppec_compat_layer, WooCommerce\PayPalCommerce\Compat\PPEC\SubscriptionsHandler->maybe_hook, WooCommerce\PayPalCommerce\Compat\PPEC\PPECHelper::use_ppec_compat_layer_for_subscriptions, WooCommerce\PayPalCommerce\Compat\PPEC\PPECHelper::site_has_ppec_subscriptions
[25-Apr-2024 09:37:45 UTC] PHP Fatal error:  Uncaught Error: Cannot use object of type WP_Error as array in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php:152
Stack trace:
#0 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(324): WooCommerce\PayPalCommerce\{closure}('')
#1 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admin/admin-ajax.php(192): do_action('wp_ajax_validat...')
#4 {main}
  thrown in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 152
[25-Apr-2024 09:39:49 UTC] PHP Notice:  Undefined index: disable-funding in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/modules/ppcp-button/src/Assets/SmartButton.php on line 1001
[25-Apr-2024 09:39:49 UTC] PHP Notice:  Undefined index: card_button_poweredby_tagline in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/modules/ppcp-button/src/Assets/SmartButton.php on line 717
[25-Apr-2024 09:42:43 UTC] PHP Fatal error:  Uncaught Error: Cannot use object of type WP_Error as array in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php:152
Stack trace:
#0 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(324): WooCommerce\PayPalCommerce\{closure}('')
#1 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admin/admin-ajax.php(192): do_action('wp_ajax_validat...')
#4 {main}
  thrown in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 152
[25-Apr-2024 09:47:32 UTC] PHP Notice:  Undefined variable: response in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 142
[25-Apr-2024 09:47:32 UTC] PHP Notice:  Trying to access array offset on value of type null in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 142
[25-Apr-2024 09:47:32 UTC] PHP Fatal error:  Uncaught TypeError: json_decode() expects parameter 1 to be string, null given in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php:142
Stack trace:
#0 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(142): json_decode(NULL)
#1 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(324): WooCommerce\PayPalCommerce\{closure}('')
#2 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#3 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#4 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admin/admin-ajax.php(192): do_action('wp_ajax_validat...')
#5 {main}
  thrown in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 142
[25-Apr-2024 09:47:42 UTC] PHP Notice:  Undefined variable: response in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 142
[25-Apr-2024 09:47:42 UTC] PHP Notice:  Trying to access array offset on value of type null in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 142
[25-Apr-2024 09:47:42 UTC] PHP Fatal error:  Uncaught TypeError: json_decode() expects parameter 1 to be string, null given in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php:142
Stack trace:
#0 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php(142): json_decode(NULL)
#1 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(324): WooCommerce\PayPalCommerce\{closure}('')
#2 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#3 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#4 /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-admin/admin-ajax.php(192): do_action('wp_ajax_validat...')
#5 {main}
  thrown in /home/<USER>/11a/6/6250f87ffa/drop2buy.com/wp-content/plugins/codarab-payment/woocommerce-paypal-payments.php on line 142
