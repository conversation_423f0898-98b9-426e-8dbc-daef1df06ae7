<?php
require __DIR__ . '/vendor/autoload.php';
use Automattic\WooCommerce\Client;

$servername = "sdb-68.hosting.stackcp.net";
$username = "wordpress-353034398ffb";
$password = "0d864e2cadc7";
$dbname = "wordpress-353034398ffb";

$Consumerkey= 'ck_40765b4f126a683b67a56e18da892c6fec8c9bd0';
$Consumersecret= 'cs_c4c725dbca1978b0997c2ee527c48ff1a0b9e144';

$woocommerce = new Client(
  'https://americasuits.imrashid.net',
  $Consumerkey,
  $Consumersecret,
  [
    'version' => 'wc/v3',
  ]
);

// Create connection
$wpdb = new mysqli($servername, $username, $password, $dbname);
// Check connection
if ($wpdb->connect_error) {
  die("Connection failed: " . $wpdb->connect_error);
}


function updateProduct($row,$woocommerce, $wpdb,$conn)
{

  $sql = "SELECT * FROM `oc_product` WHERE inserting=1 AND product_id=".$row['product_id'];
    $check = $conn->query($sql);

    if ($check->num_rows > 0) {
      return;
    }
    else
    {
      $sql = "UPDATE `oc_product` SET inserting=1 WHERE product_id=".$row['product_id'];
      $conn->query($sql);
    }
    
  $data = [
    'sku' => $row['model']
  ];

  $res = $woocommerce->put('products/'.$row['wp_product_id'], $data);

  $sql = "UPDATE `oc_product` SET skuupdate=1 WHERE product_id=".$row['product_id'];
  $conn->query($sql);

  echo $res->sku."\n";
}


function addProduct($row,$woocommerce, $wpdb,$conn)
{
  try {
    echo $row['product_id'];

    $sql = "SELECT * FROM `oc_product` WHERE inserting=1 AND product_id=".$row['product_id'];
    $check = $conn->query($sql);

    if ($check->num_rows > 0) {
      return;
    }
    else
    {
      $sql = "UPDATE `oc_product` SET inserting=1 WHERE product_id=".$row['product_id'];
      $conn->query($sql);
    }
         

        $row['description']=html_entity_decode($row['description']);
        $row['description']=utf8_decode($row['description']);
        
        $images = [];
        $categories=[];
        $attributes=[];
        $attributes_data=[];

        $images[]=array(
          'src'=>'https://americasuits.com/image/'.$row['image']
        );

        $sql = "SELECT * FROM `oc_product_image` WHERE product_id=".$row['product_id'];
        $result = $conn->query($sql);

        if ($result->num_rows > 0) {
          // output data of each row
          while($img = $result->fetch_assoc()) {
            $images[]=array(
              'src'=>'https://americasuits.com/image/'.$img['image']
            );
          }
        }

        $sql = "SELECT `oc_category`.`wp_category_id` FROM `oc_category`
        INNER JOIN `oc_product_to_category` ON `oc_product_to_category`.`category_id` = `oc_category`.`category_id`
        WHERE `oc_product_to_category`.`product_id`=".$row['product_id'];
        $result = $conn->query($sql);

        if ($result->num_rows > 0) {
          // output data of each row
          while($cat = $result->fetch_assoc()) {
            $categories[]=array(
              'id'=>$cat['wp_category_id']
            );
          }
        }


        $sql = "SELECT `oc_product_option_value`.*,`oc_option_value_description`.*, `oc_option_description`.`name` AS option_name FROM `oc_product_option_value`
        INNER JOIN `oc_option_value_description` ON `oc_option_value_description`.`option_value_id` = `oc_product_option_value`.`option_value_id`
        INNER JOIN `oc_option_description` ON `oc_option_description`.`option_id` = `oc_option_value_description`.`option_id`
        WHERE `oc_product_option_value`.`product_id`=".$row['product_id'];
        $result = $conn->query($sql);

        $atr_temp=[];

        if ($result->num_rows > 0) {
          // output data of each row
          while($atr = $result->fetch_assoc()) {
            $atr_temp[$atr['option_name']][] = $atr;
          }
        }

        foreach($atr_temp as $key => $value)
        {
          $options = [];
          $attributes_op=[];
          foreach($value as $vrow)
          {
            $options[]=$vrow['name'];
            $attributes_op[]=array(
              'option'=>$vrow['name']
            );
          }
          $attributes[]=array( 
            'name'=>$key,
            'type'=>'select',
            'slug'=>'size',
            'position'=>'0',
            'visible'=>'false',
            'variation'=>'true',
            'options'=>$options
          );

          $attributes_data[]=[
            'regular_price' => $row['price'],
            'sale_price' => $row['s_price'],
            'attributes' => $attributes_op
          ];
        }

        $data = [
          'name' => $row['name'],
          "slug" => $row['keyword'],
          'stock_status' => 'instock',
          'type' => 'variable',
          'regular_price' => $row['price'],
          'sale_price' => $row['s_price'],
          "description" => $row['description'], 
          'short_description' => '',
          'attributes' => $attributes,
          'categories' => $categories,
          'images' => $images
      ];

      $responce = $woocommerce->post('products', $data);

      $sql = "UPDATE `oc_product` SET `wp_product_id`='".$responce->id."' WHERE `product_id`='".$row['product_id']."'";
      $conn->query($sql);


      foreach($attributes_data as $rdata)
      {
        $woocommerce->post('products/'.$responce->id.'/variations', $rdata);
      }



      $sql = "INSERT INTO `06_postmeta` SET 
      `post_id`='".$responce->id."',
      `meta_key`='rank_math_title',
      `meta_value`='".$wpdb->real_escape_string($row['meta_title'])."'
      ";
      $wpdb->query($sql);

      $sql = "INSERT INTO `06_postmeta` SET 
      `post_id`='".$responce->id."',
      `meta_key`='rank_math_description',
      `meta_value`='".$wpdb->real_escape_string($row['meta_description'])."'
      ";
      $wpdb->query($sql);

      $sql = "INSERT INTO `06_postmeta` SET 
      `post_id`='".$responce->id."',
      `meta_key`='rank_math_focus_keyword',
      `meta_value`='".$wpdb->real_escape_string($row['meta_keyword'])."'
      ";
      $wpdb->query($sql);

      echo '-'.$responce->id."\n";
    
  }
  catch(Exception $e) {
    echo 'Message: ' .$e->getMessage()."\n\n";
    sleep(3);
    $servername = "sdb-68.hosting.stackcp.net";
    $username = "wordpress-353034398ffb";
    $password = "0d864e2cadc7";
    $dbname = "wordpress-353034398ffb";
    // Create connection
    $wpdb = new mysqli($servername, $username, $password, $dbname);
    // Check connection
    if ($wpdb->connect_error) {
      die("Connection failed: " . $wpdb->connect_error);
    }

    $servername = "sdb-67.hosting.stackcp.net";
    $username = "americasuits-35303437083b";
    $password = "Abc123##";
    $dbname = "americasuits-35303437083b";

    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    // Check connection
    if ($conn->connect_error) {
      die("Connection failed: " . $conn->connect_error);
    }
    addProduct($row,$woocommerce, $wpdb,$conn);
  }

  

}

function updateCategory($row,$woocommerce, $wpdb,$conn)
{
  $row['description']=html_entity_decode($row['description']);
  $row['description']=utf8_decode($row['description']);

  $data = [
    'description' => $row['description']
  ];
  $woocommerce->put('products/categories/'.$row['wp_category_id'], $data);

  echo $row['wp_category_id']."\n";
}


function addCategory($row,$woocommerce, $wpdb,$conn,$parent_id=0)
{

  $row['description']=utf8_decode($row['description']);

  echo $parent_id.'-'.$row['category_id'];
    if(empty($row['name'])){return 0;}
    $data = [
        'name' => $row['name'],
        "slug" => $row['keyword'],
        "parent" => $parent_id,
        "description" => $conn->real_escape_string(html_entity_decode($row['description'])), 
        'image' => [
            'src' => 'http://demo.woothemes.com/woocommerce/wp-content/uploads/sites/56/2013/06/T_2_front.jpg'
        ]
      ];

      if(!empty($row['image']))
      {
        $data['image']=array(
            'src' => 'https://americasuits.com/image/'.$row['image']
        );
      }

     $responce = $woocommerce->post('products/categories', $data);

     echo '-'.$responce->id."\n";


     $sql = "UPDATE `oc_category` SET `wp_category_id`='".$responce->id."' WHERE `category_id`='".$row['category_id']."'";
     $conn->query($sql);

     $sql = "INSERT INTO `06_termmeta` SET 
     `term_id`='".$responce->id."',
     `meta_key`='rank_math_title',
     `meta_value`='".$row['meta_title']."'
     ";
     $wpdb->query($sql);

     $sql = "INSERT INTO `06_termmeta` SET 
     `term_id`='".$responce->id."',
     `meta_key`='rank_math_description',
     `meta_value`='".$row['meta_description']."'
     ";
     $wpdb->query($sql);

     $sql = "INSERT INTO `06_termmeta` SET 
     `term_id`='".$responce->id."',
     `meta_key`='rank_math_focus_keyword',
     `meta_value`='".$row['meta_keyword']."'
     ";
     $wpdb->query($sql);

     $sql = "SELECT * FROM `oc_category` 
        INNER JOIN `oc_category_description` ON `oc_category_description`.`category_id` = `oc_category`.`category_id` AND `oc_category_description`.`language_id`=1
        INNER JOIN `oc_seo_url` ON `oc_seo_url`.`query`=CONCAT('category_id=',`oc_category`.`category_id`) AND `oc_seo_url`.`language_id`=1
        WHERE `oc_category`.`parent_id`=".$row['category_id']." AND `wp_category_id`=0 ORDER BY `oc_category`.`category_id` asc";
     $result = $conn->query($sql);

        if ($result->num_rows > 0) {
            // output data of each row
            while($rowc = $result->fetch_assoc()) {
                addCategory($rowc,$woocommerce,$wpdb,$conn,$responce->id);   
            }
        }
}