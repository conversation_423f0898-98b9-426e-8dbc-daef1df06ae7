<?php
include "wordpress-db.php";
include "opencart-db.php";

$sql = "SELECT * FROM `oc_category` 
INNER JOIN `oc_category_description` ON `oc_category_description`.`category_id` = `oc_category`.`category_id` AND `oc_category_description`.`language_id`=1
INNER JOIN `oc_seo_url` ON `oc_seo_url`.`query`=CONCAT('category_id=',`oc_category`.`category_id`) AND `oc_seo_url`.`language_id`=1
ORDER BY `oc_category`.`category_id` asc";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
  // output data of each row
  while($row = $result->fetch_assoc()) {
    updateCategory($row,$woocommerce, $wpdb,$conn);
  }
} else {
  echo "0 results";
}
$conn->close();



















exit();

$sql = "SELECT * FROM `oc_category` 
INNER JOIN `oc_category_description` ON `oc_category_description`.`category_id` = `oc_category`.`category_id` AND `oc_category_description`.`language_id`=1
INNER JOIN `oc_seo_url` ON `oc_seo_url`.`query`=CONCAT('category_id=',`oc_category`.`category_id`) AND `oc_seo_url`.`language_id`=1
WHERE `oc_category`.`parent_id`=0 AND `wp_category_id`=0 ORDER BY `oc_category`.`category_id` asc";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
  // output data of each row
  while($row = $result->fetch_assoc()) {
    addCategory($row,$woocommerce,$wpdb,$conn);  
  }
} else {
  echo "0 results";
}
$conn->close();
