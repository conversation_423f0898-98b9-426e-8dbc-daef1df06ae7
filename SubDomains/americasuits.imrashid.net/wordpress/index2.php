<?php

$Consumerkey= 'ck_40765b4f126a683b67a56e18da892c6fec8c9bd0';
$Consumersecret= 'cs_c4c725dbca1978b0997c2ee527c48ff1a0b9e144';


require __DIR__ . '/vendor/autoload.php';

use Automattic\WooCommerce\Client;

$woocommerce = new Client(
  'https://americasuits.imrashid.net',
  $Consumerkey,
  $Consumersecret,
  [
    'version' => 'wc/v3',
  ]
);


$data = [
    'name' => 'Premium Quality yyyy',
    "slug"=> "premium-quality-19",
    'stock_status' => 'instock',
    'type' => 'variable',
    'regular_price' => '21.99',
    'sale_price' => '12',
    'description' => 'Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.',
    'short_description' => 'Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.',
    'attributes' => array( 
        array( 
            'name'=>'Size',
            'type'=>'select',
            'slug'=>'size',
            'position'=>'0',
            'visible'=>'false',
            'variation'=>'true',
            'options'=>array( "XS","XXL","S","M","L","XL","2XL","3XL")
        ) 
     ),
    'categories' => [
        [
            'id' => 9
        ],
        [
            'id' => 14
        ]
    ],
    'images' => [
        [
            'src' => 'http://demo.woothemes.com/woocommerce/wp-content/uploads/sites/56/2013/06/T_2_front.jpg'
        ],
        [
            'src' => 'http://demo.woothemes.com/woocommerce/wp-content/uploads/sites/56/2013/06/T_2_back.jpg'
        ]
    ]
];


$response = $woocommerce->post('products', $data);

echo $response->id;


$data = [
    'regular_price' => '21.99',
    'sale_price' => '12',
    'attributes' => [
        [
            'option' => "XS",
            'option' => "XXL",
            'option' => "S",
            'option' => "M",
            'option' => "L",
            'option' => "XL",
            'option' => "2XL",
            'option' => "3XL"
        ]
    ]
];

print_r($woocommerce->post('products/'.$response->id.'/variations', $data));
?>