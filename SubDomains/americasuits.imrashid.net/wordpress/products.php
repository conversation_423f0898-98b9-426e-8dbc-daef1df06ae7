<?php
include "wordpress-db.php";
include "opencart-db.php";



$sql = "SELECT * FROM oc_product WHERE wp_product_id<>0 and `inserting`=0  ORDER BY product_id ASC";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
  // output data of each row
  while($row = $result->fetch_assoc()) {
    updateProduct($row,$woocommerce, $wpdb,$conn);
  }
} else {
  echo "0 results";
}
$conn->close();












exit();

$sql = "SELECT `oc_product`.*,`oc_product_description`.*, `oc_product_special`.`price` AS s_price, `oc_seo_url`.* FROM `oc_product`
INNER JOIN `oc_product_description` ON `oc_product_description`.`product_id` = `oc_product`.`product_id` AND `oc_product_description`.`language_id`=1
INNER JOIN `oc_product_special` ON `oc_product_special`.`product_id`=`oc_product`.`product_id`
INNER JOIN `oc_seo_url` ON `oc_seo_url`.`query`=CONCAT('product_id=',`oc_product`.`product_id`) AND `oc_seo_url`.`language_id`=1
WHERE `wp_product_id`=0 and `inserting`=0 ORDER BY `oc_product`.`product_id` ASC;";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
  // output data of each row
  while($row = $result->fetch_assoc()) {
    addProduct($row,$woocommerce, $wpdb,$conn);
  }
} else {
  echo "0 results";
}
$conn->close();